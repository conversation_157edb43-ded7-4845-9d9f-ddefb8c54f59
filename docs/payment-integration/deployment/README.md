# 🚀 Payment Integration Deployment Guide

## 🎯 Overview
Comprehensive deployment guide for the payment management integration across purchase orders and goods receipts in the pharmacy application.

## 📋 Pre-Deployment Checklist

### Development Environment
- [ ] All tests passing (unit, integration, E2E)
- [ ] Type checking clean (`bun run type-check`)
- [ ] Linting clean (`bun run lint`)
- [ ] Code review completed
- [ ] Documentation updated
- [ ] Performance benchmarks met

### Database Preparation
- [ ] Database migrations ready
- [ ] Backup procedures tested
- [ ] Rollback plan prepared
- [ ] Data integrity checks completed

### Infrastructure
- [ ] Environment variables configured
- [ ] SSL certificates valid
- [ ] CDN configuration updated
- [ ] Monitoring alerts configured

---

## 🗄️ Database Migration

### Migration Scripts

#### 1. Add Payment Context Fields
```sql
-- Add payment context to supplier_payments table
ALTER TABLE supplier_payments 
ADD COLUMN payment_type VARCHAR(20) DEFAULT 'FINAL',
ADD COLUMN payment_category VARCHAR(20) DEFAULT 'COMPLETION',
ADD COLUMN business_stage VARCHAR(20) DEFAULT 'COMPLETION',
ADD COLUMN due_date_basis VARCHAR(20) DEFAULT 'ORDER_DATE',
ADD COLUMN effective_date TIMESTAMP,
ADD COLUMN calculated_due_date TIMESTAMP;

-- Add indexes for performance
CREATE INDEX idx_supplier_payments_payment_type ON supplier_payments(payment_type);
CREATE INDEX idx_supplier_payments_business_stage ON supplier_payments(business_stage);
CREATE INDEX idx_supplier_payments_calculated_due_date ON supplier_payments(calculated_due_date);
```

#### 2. Add Goods Receipt Payment Fields
```sql
-- Add payment fields to goods_receipts table
ALTER TABLE goods_receipts
ADD COLUMN payment_status VARCHAR(20) DEFAULT 'PENDING',
ADD COLUMN delivery_confirmed_at TIMESTAMP,
ADD COLUMN invoice_matched BOOLEAN DEFAULT FALSE,
ADD COLUMN invoice_number VARCHAR(100),
ADD COLUMN invoice_date TIMESTAMP,
ADD COLUMN invoice_amount DECIMAL(15,2);

-- Add indexes
CREATE INDEX idx_goods_receipts_payment_status ON goods_receipts(payment_status);
CREATE INDEX idx_goods_receipts_invoice_number ON goods_receipts(invoice_number);
```

#### 3. Create Payment Commitment Table
```sql
-- Create payment_commitments table
CREATE TABLE payment_commitments (
  id VARCHAR(36) PRIMARY KEY,
  purchase_order_id VARCHAR(36) NOT NULL,
  commitment_type VARCHAR(20) NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  commitment_date TIMESTAMP NOT NULL,
  status VARCHAR(20) DEFAULT 'PLANNED',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE
);

CREATE INDEX idx_payment_commitments_po_id ON payment_commitments(purchase_order_id);
CREATE INDEX idx_payment_commitments_status ON payment_commitments(status);
```

#### 4. Create Invoice Matching Table
```sql
-- Create invoice_matching table
CREATE TABLE invoice_matching (
  id VARCHAR(36) PRIMARY KEY,
  goods_receipt_id VARCHAR(36) NOT NULL,
  invoice_number VARCHAR(100) NOT NULL,
  invoice_date TIMESTAMP NOT NULL,
  invoice_amount DECIMAL(15,2) NOT NULL,
  goods_receipt_amount DECIMAL(15,2) NOT NULL,
  matching_status VARCHAR(20) DEFAULT 'PENDING',
  discrepancy_reason TEXT,
  matched_by VARCHAR(36),
  matched_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (goods_receipt_id) REFERENCES goods_receipts(id) ON DELETE CASCADE,
  FOREIGN KEY (matched_by) REFERENCES users(id)
);

CREATE INDEX idx_invoice_matching_gr_id ON invoice_matching(goods_receipt_id);
CREATE INDEX idx_invoice_matching_invoice_number ON invoice_matching(invoice_number);
```

### Migration Execution
```bash
# Run migrations
cd packages/backend
bun run migration:run

# Verify migrations
bun run migration:status

# Rollback if needed
bun run migration:rollback
```

---

## 🔧 Environment Configuration

### Frontend Environment Variables
```bash
# packages/frontend/.env.production
NEXT_PUBLIC_API_URL=https://api.pharmacy.yourdomain.com
NEXT_PUBLIC_APP_URL=https://pharmacy.yourdomain.com
NEXT_PUBLIC_PAYMENT_FEATURE_ENABLED=true
NEXT_PUBLIC_INVOICE_UPLOAD_MAX_SIZE=10485760
NEXT_PUBLIC_PAYMENT_TERMS_MAX_DAYS=365
```

### Backend Environment Variables
```bash
# packages/backend/.env.production
DATABASE_URL=postgresql://user:password@localhost:5432/pharmacy_prod
PAYMENT_PROCESSING_ENABLED=true
INVOICE_STORAGE_PATH=/var/uploads/invoices
PAYMENT_NOTIFICATION_WEBHOOK_URL=https://notifications.yourdomain.com/webhook
BUSINESS_DAYS_CALCULATION_ENABLED=true
INDONESIAN_HOLIDAYS_API_URL=https://api.harilibur.id
```

---

## 📦 Build Process

### Frontend Build
```bash
cd packages/frontend

# Install dependencies
bun install

# Type check
bun run type-check

# Build for production
bun run build

# Test production build
bun run start
```

### Backend Build
```bash
cd packages/backend

# Install dependencies
bun install

# Build TypeScript
bun run build

# Run production server
bun run start:prod
```

---

## 🐳 Docker Deployment

### Dockerfile Updates

#### Frontend Dockerfile
```dockerfile
# packages/frontend/Dockerfile
FROM node:18-alpine AS base
RUN npm install -g bun

FROM base AS deps
WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN bun run build

FROM base AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["bun", "server.js"]
```

#### Backend Dockerfile
```dockerfile
# packages/backend/Dockerfile
FROM node:18-alpine AS base
RUN npm install -g bun

FROM base AS deps
WORKDIR /app
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN bun run build

FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

RUN mkdir -p /var/uploads/invoices
RUN chown -R node:node /var/uploads

USER node
EXPOSE 3001

CMD ["bun", "run", "start:prod"]
```

### Docker Compose Updates
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001
    depends_on:
      - backend

  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=**************************************/pharmacy
      - PAYMENT_PROCESSING_ENABLED=true
    volumes:
      - invoice_uploads:/var/uploads/invoices
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=pharmacy
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/backend/migrations:/docker-entrypoint-initdb.d

volumes:
  postgres_data:
  invoice_uploads:
```

---

## 🔄 Deployment Process

### Blue-Green Deployment

#### 1. Prepare Green Environment
```bash
# Deploy to green environment
docker-compose -f docker-compose.green.yml up -d

# Run health checks
curl -f http://green.pharmacy.yourdomain.com/health

# Run smoke tests
bun run test:smoke:green
```

#### 2. Database Migration
```bash
# Run migrations on green environment
docker exec pharmacy-backend-green bun run migration:run

# Verify data integrity
docker exec pharmacy-backend-green bun run migration:verify
```

#### 3. Switch Traffic
```bash
# Update load balancer configuration
# Switch traffic from blue to green
nginx -s reload

# Monitor metrics
# Verify payment functionality
```

#### 4. Cleanup
```bash
# After successful deployment
docker-compose -f docker-compose.blue.yml down

# Clean up old images
docker image prune -f
```

---

## 📊 Monitoring & Health Checks

### Health Check Endpoints

#### Frontend Health Check
```typescript
// packages/frontend/src/app/api/health/route.ts
export async function GET() {
  try {
    // Check payment feature availability
    const paymentFeatureEnabled = process.env.NEXT_PUBLIC_PAYMENT_FEATURE_ENABLED === 'true';
    
    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      features: {
        paymentManagement: paymentFeatureEnabled,
      },
    });
  } catch (error) {
    return Response.json(
      { status: 'unhealthy', error: error.message },
      { status: 500 }
    );
  }
}
```

#### Backend Health Check
```typescript
// packages/backend/src/health/health.controller.ts
@Get('health')
async getHealth() {
  const dbHealth = await this.checkDatabaseConnection();
  const paymentHealth = await this.checkPaymentService();
  
  return {
    status: dbHealth && paymentHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    services: {
      database: dbHealth ? 'up' : 'down',
      paymentProcessing: paymentHealth ? 'up' : 'down',
    },
  };
}
```

### Monitoring Metrics
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'pharmacy-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'

  - job_name: 'pharmacy-backend'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/metrics'

  - job_name: 'payment-processing'
    static_configs:
      - targets: ['backend:3001']
    metrics_path: '/payment-metrics'
```

---

## 🚨 Rollback Procedures

### Automatic Rollback Triggers
- Health check failures > 5 minutes
- Error rate > 5%
- Payment processing failures > 10%
- Database connection failures

### Manual Rollback Process
```bash
# 1. Stop green environment
docker-compose -f docker-compose.green.yml down

# 2. Restore blue environment
docker-compose -f docker-compose.blue.yml up -d

# 3. Rollback database if needed
docker exec pharmacy-backend-blue bun run migration:rollback

# 4. Update load balancer
nginx -s reload

# 5. Verify rollback
curl -f http://pharmacy.yourdomain.com/health
bun run test:smoke
```

---

## 📋 Post-Deployment Verification

### Functional Tests
- [ ] Purchase order payment creation
- [ ] Goods receipt payment processing
- [ ] Invoice matching workflow
- [ ] Payment status updates
- [ ] Due date calculations
- [ ] Payment compliance monitoring

### Performance Tests
- [ ] Page load times < 3 seconds
- [ ] API response times < 500ms
- [ ] Database query performance
- [ ] Payment processing throughput

### Security Tests
- [ ] Authentication working
- [ ] Authorization enforced
- [ ] Input validation active
- [ ] SQL injection protection
- [ ] XSS protection enabled

---

## 📞 Support & Troubleshooting

### Common Issues

#### Payment Modal Not Loading
```bash
# Check frontend logs
docker logs pharmacy-frontend

# Verify API connectivity
curl -f http://backend:3001/api/purchase-orders/1/payment-summary
```

#### Database Connection Issues
```bash
# Check database connectivity
docker exec pharmacy-backend pg_isready -h db -p 5432

# Verify migrations
docker exec pharmacy-backend bun run migration:status
```

#### Invoice Upload Failures
```bash
# Check upload directory permissions
docker exec pharmacy-backend ls -la /var/uploads/invoices

# Verify file size limits
docker exec pharmacy-backend cat /proc/sys/fs/file-max
```

### Emergency Contacts
- **DevOps Team**: <EMAIL>
- **Backend Team**: <EMAIL>
- **Frontend Team**: <EMAIL>
- **Database Team**: <EMAIL>

---

## 📈 Performance Optimization

### Database Optimization
```sql
-- Add composite indexes for common queries
CREATE INDEX idx_payments_po_status_date ON supplier_payments(purchase_order_id, status, payment_date);
CREATE INDEX idx_payments_gr_status_date ON supplier_payments(goods_receipt_id, status, payment_date);

-- Optimize payment summary queries
CREATE MATERIALIZED VIEW payment_summary_view AS
SELECT 
  purchase_order_id,
  SUM(amount) as total_paid,
  COUNT(*) as payment_count,
  MAX(payment_date) as last_payment_date
FROM supplier_payments 
WHERE status = 'PAID'
GROUP BY purchase_order_id;

-- Refresh materialized view periodically
REFRESH MATERIALIZED VIEW payment_summary_view;
```

### Frontend Optimization
```typescript
// Implement payment data caching
const paymentQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Use React.memo for expensive components
export const PaymentSummaryCard = React.memo(PaymentSummaryCardComponent);
```

---

**Deployment Complete! 🎉**

The payment management integration is now ready for production use across your pharmacy application.
