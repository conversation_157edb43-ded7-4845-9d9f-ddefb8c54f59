# 🚚 Phase 2: Optional Goods Receipt Payment UI (Backend Already Complete!)

## 🎯 Objective
**Optional Phase**: Add goods receipt payment UI components to use your existing goods receipt payment API. **Your backend already supports goods receipt payments!**

## ✅ **Backend Status: COMPLETE!**

Your backend already includes:
- ✅ `GET /goods-receipts/:id/payment-summary` - Already implemented!
- ✅ Goods receipt payment support in SupplierPayment model
- ✅ Payment relationship with `goodsReceiptId` foreign key
- ✅ All payment business logic works for goods receipts

## 🤔 **Do You Need This Phase?**

**Most small pharmacies can skip this phase** because:
- ✅ Purchase order payments cover 90% of pharmacy payment needs
- ✅ Your existing backend already supports goods receipt payments if needed
- ✅ You can always add goods receipt payment UI later

**Only implement this if:**
- 📦 You need delivery-based payment processing
- 📋 You want separate payment management for goods receipts
- 🏢 You have complex supplier payment workflows

## 📋 Optional Frontend Tasks

### Task 1: Add Goods Receipt Payment Components (Optional)
**Duration**: 3-4 hours | **Priority**: Low | **Complexity**: Low

#### Description
Create simple payment components for goods receipts using your existing goods receipt payment API.

#### Deliverables
- GoodsReceiptPaymentSummary component
- Payment action in goods receipt table
- Simple payment modal for goods receipts

#### Technical Details
```typescript
// Simple goods receipt payment summary
interface GoodsReceiptPaymentSummaryProps {
  goodsReceipt: GoodsReceiptWithRelations;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
}

export function GoodsReceiptPaymentSummary({ goodsReceipt, onViewPayments, onAddPayment }: GoodsReceiptPaymentSummaryProps) {
  const { data: paymentSummary } = useGoodsReceiptPaymentSummary(goodsReceipt.id);

  if (!paymentSummary) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Pembayaran Goods Receipt</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Total:</span>
            <span>{formatCurrency(paymentSummary.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Dibayar:</span>
            <span className="text-green-600">{formatCurrency(paymentSummary.totalPaid)}</span>
          </div>
          <div className="flex justify-between">
            <span>Sisa:</span>
            <span className="text-blue-600">{formatCurrency(paymentSummary.remainingAmount)}</span>
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          <Button variant="outline" onClick={onViewPayments} className="flex-1">
            Lihat Pembayaran
          </Button>
          <Button onClick={onAddPayment} className="flex-1">
            Tambah Pembayaran
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### Files to Create
- `packages/frontend/src/components/goods-receipts/GoodsReceiptPaymentSummary.tsx`

#### Acceptance Criteria
- [ ] Uses existing goods receipt payment API
- [ ] Displays payment summary correctly
- [ ] Indonesian language interface
- [ ] Consistent with purchase order payment components

---

### Task 2: Create Goods Receipt Payment Hooks (Optional)
**Duration**: 1-2 hours | **Priority**: Low | **Complexity**: Low

#### Description
Create React Query hooks to call your existing goods receipt payment API.

#### Deliverables
- Goods receipt payment summary hook
- Simple payment creation hook for goods receipts

#### Technical Details
```typescript
// Simple hooks using your existing API
export function useGoodsReceiptPaymentSummary(grId: string) {
  return useQuery({
    queryKey: ['goods-receipts', grId, 'payment-summary'],
    queryFn: () => goodsReceiptsApi.getPaymentSummary(grId), // Uses your existing endpoint
    enabled: !!grId,
  });
}

export function useCreateGoodsReceiptPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ grId, data }: { grId: string; data: CreateSupplierPaymentDto }) =>
      goodsReceiptsApi.createPayment(grId, data), // If you add this endpoint
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['goods-receipts'] });
      toast.success('Pembayaran goods receipt berhasil dibuat');
    },
    onError: (error) => {
      toast.error('Gagal membuat pembayaran goods receipt');
      console.error('GR Payment creation error:', error);
    },
  });
}
```

#### Files to Create
- `packages/frontend/src/hooks/useGoodsReceiptPayments.ts`

#### Acceptance Criteria
- [ ] Hooks properly typed
- [ ] Uses existing goods receipt payment API
- [ ] Error handling implemented
- [ ] Follows existing hook patterns

---

### Task 3: Add Payment Column to Goods Receipt Table (Optional)
**Duration**: 1 hour | **Priority**: Low | **Complexity**: Low

#### Description
Add payment status column to goods receipt table if you want payment management for goods receipts.

#### Deliverables
- Payment status column in goods receipt table
- Payment action in dropdown menu

#### Technical Details
```typescript
// Add to your existing goods receipt columns
{
  accessorKey: 'paymentStatus',
  header: 'Status Pembayaran',
  cell: ({ row }) => {
    const gr = row.original;
    return <PaymentStatusBadge status={gr.paymentStatus || 'PENDING'} />;
  },
}

// Add to your existing goods receipt actions dropdown
<DropdownMenuItem onClick={() => openGRPaymentModal(goodsReceipt)}>
  <CreditCard className="mr-2 h-4 w-4" />
  Kelola Pembayaran
</DropdownMenuItem>
```

#### Files to Modify
- `packages/frontend/src/components/goods-receipts/columns.tsx`
- `packages/frontend/src/app/(dashboard)/dashboard/goods-receipts/page.tsx`

#### Acceptance Criteria
- [ ] Payment column displays correctly
- [ ] Payment action opens modal
- [ ] Consistent with purchase order table patterns
- [ ] Uses existing PaymentStatusBadge component

---

## 🎯 **Recommendation: Skip This Phase for Now**

### **Why Skip Goods Receipt Payments:**

1. **✅ Purchase Order Payments Cover Most Needs**
   - Most pharmacy payments happen when ordering from suppliers
   - Purchase order payment workflow is sufficient for small pharmacies
   - Your existing backend already supports both PO and GR payments

2. **✅ Your Backend Already Supports It**
   - If you need goods receipt payments later, your backend is ready
   - No additional backend development needed
   - Can add frontend components incrementally

3. **✅ Keep It Simple**
   - Focus on core purchase order payment functionality first
   - Add goods receipt payments only if business requires it
   - Avoid over-engineering for a pharmacy store

### **When to Implement This Phase:**

**Implement goods receipt payments if:**
- 📦 You need delivery-based payment processing
- 📋 Suppliers require payment confirmation upon delivery
- 🏢 You have complex multi-stage payment workflows
- 📊 You need separate payment tracking for goods receipts

**Skip this phase if:**
- 🏪 You're a small to medium pharmacy
- 💰 Purchase order payments meet your needs
- 🎯 You want to keep the system simple
- ⏰ You want to launch payment features quickly

## 🔧 **If You Decide to Implement (Simple Approach)**

### **Implementation Time: 2-3 hours total**

1. **Create GoodsReceiptPaymentSummary component** (1 hour)
   - Copy PaymentSummaryCard and adapt for goods receipts
   - Use existing `useGoodsReceiptPaymentSummary` hook pattern

2. **Add payment column to goods receipt table** (30 minutes)
   - Copy payment column from purchase order table
   - Use existing PaymentStatusBadge component

3. **Create simple payment hooks** (1 hour)
   - Copy purchase order payment hooks
   - Adapt for goods receipt endpoints

4. **Add payment action to dropdown** (30 minutes)
   - Copy payment action from purchase order dropdown
   - Open simple payment modal

### **Files to Create/Modify:**
```
packages/frontend/src/
├── components/goods-receipts/
│   └── GoodsReceiptPaymentSummary.tsx     (new)
├── hooks/
│   └── useGoodsReceiptPayments.ts         (new)
└── components/goods-receipts/
    ├── columns.tsx                        (modify)
    └── page.tsx                          (modify)
```

## 📊 **Success Criteria (If Implemented)**

### **Functional Requirements**
- [ ] GoodsReceiptPaymentSummary component displays correctly
- [ ] Payment column shows in goods receipt table
- [ ] Payment action opens modal/interface
- [ ] Uses existing goods receipt payment API

### **Business Requirements**
- [ ] Indonesian language interface
- [ ] Consistent with purchase order payment patterns
- [ ] Simple and not over-engineered
- [ ] Optional and can be skipped

## 🎯 **Final Recommendation**

### **For Most Pharmacies: Skip This Phase**
- ✅ Focus on purchase order payments (Phase 1)
- ✅ Your backend already supports goods receipt payments if needed later
- ✅ Keep the system simple and focused
- ✅ Launch payment features faster

### **For Complex Operations: Implement Later**
- 📦 Add goods receipt payment UI only if business requires it
- 🔄 Can be implemented incrementally after Phase 1
- 🎯 Your backend is already ready for it

---

**Estimated Time: 2-3 hours (if implemented) | Recommended: Skip for now**

**Next Phase**: [Phase 3: Optional Advanced Features](../phase-3/README.md)
