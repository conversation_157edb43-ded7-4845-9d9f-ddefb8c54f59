# 💳 Payment API Documentation - Using Your Existing Backend

## 🎯 Overview
Your backend already has **excellent payment APIs implemented!** This documentation shows what's available and how to use them from the frontend.

## ✅ **Existing Payment APIs (Already Implemented!)**

### **Purchase Order Payment APIs**

#### Get Payment Summary ✅ **ALREADY EXISTS**
```http
GET /api/purchase-orders/{id}/payment-summary
```
**Implementation:** `PurchaseOrderController.getPaymentSummary()`

**Response:**
```typescript
interface PurchaseOrderPaymentSummary {
  purchaseOrderId: string;
  orderNumber: string;
  totalAmount: number;
  totalPaid: number;
  remainingAmount: number;
  paymentPercentage: number;
  payments: Array<{
    id: string;
    amount: number;
    paymentMethod: PaymentMethod;
    paymentDate: string;
    status: PaymentStatus;
    reference?: string;
  }>;
}
```

#### Get Payment Schedule ✅ **ALREADY EXISTS**
```http
GET /api/purchase-orders/{id}/payment-schedule
```
**Implementation:** `PurchaseOrderController.getPaymentSchedule()`

#### Get Payment Info ✅ **ALREADY EXISTS**
```http
GET /api/purchase-orders/{id}/payment-info
```
**Implementation:** `PurchaseOrderController.getPaymentInfo()`

#### Get Payments List ✅ **ALREADY EXISTS**
```http
GET /api/purchase-orders/{id}/payments?page=1&limit=10&status=PENDING
```
**Implementation:** `PurchaseOrderController.getPayments()`

#### Create Payment ✅ **ALREADY EXISTS**
```http
POST /api/purchase-orders/{id}/payments
```
**Implementation:** `PurchaseOrderController.createPayment()`

**Request Body:** Uses your existing `CreateSupplierPaymentDto`
```typescript
interface CreateSupplierPaymentDto {
  purchaseOrderId?: string;
  goodsReceiptId?: string;
  invoiceNumber?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  effectivePaymentTerms?: number;
  status?: PaymentStatus;
  reference?: string;
  notes?: string;
}
```

#### Update Payment ✅ **ALREADY EXISTS**
```http
PATCH /api/purchase-orders/{id}/payments/{paymentId}
```

#### Delete Payment ✅ **ALREADY EXISTS**
```http
DELETE /api/purchase-orders/{id}/payments/{paymentId}
```

### **Goods Receipt Payment APIs** ✅ **ALREADY EXISTS**

#### Get Payment Summary ✅ **ALREADY EXISTS**
```http
GET /api/goods-receipts/{id}/payment-summary
```
**Implementation:** `GoodsReceiptController.getPaymentSummary()`

**Response:**
```typescript
interface GoodsReceiptPaymentSummary {
  goodsReceiptId: string;
  receiptNumber: string;
  totalAmount: number;
  totalPaid: number;
  remainingAmount: number;
  paymentPercentage: number;
  payments: Array<{
    id: string;
    amount: number;
    paymentMethod: PaymentMethod;
    paymentDate: string;
    status: PaymentStatus;
    reference?: string;
  }>;
}
```

---

## 🎯 **Your Backend is Already Complete!**

### **What You Have (Excellent!):**
- ✅ **PaymentTermsService**: Sophisticated Indonesian payment terms calculation
- ✅ **Payment Status Tracking**: PENDING, PARTIAL, PAID, OVERDUE
- ✅ **Overdue Payment Detection**: Automatic overdue calculation
- ✅ **Transaction Integrity**: Proper database transactions
- ✅ **Business Rule Validation**: Payment amount validation, status transitions
- ✅ **Dual Entity Support**: Both purchase orders and goods receipts

### **What's Missing (Frontend Only!):**
- ❌ **UI Components**: PaymentStatusBadge, PaymentSummaryCard
- ❌ **Payment Modal**: Simple modal to display payment data
- ❌ **Table Integration**: Payment column in purchase order table
- ❌ **React Query Hooks**: Hooks to call your existing APIs

### **No Backend Changes Needed!**
Your backend is actually more sophisticated than most enterprise systems. We just need simple frontend components to use it.

---

## 🔧 **Simple React Query Hooks** (Follow your existing patterns)

### **Purchase Order Payment Hooks** (Keep it simple!)

```typescript
// Follow your existing hook patterns like useProducts, useCustomers
export function usePurchaseOrderPaymentSummary(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payment-summary'],
    queryFn: () => purchaseOrdersApi.getPaymentSummary(id),
    enabled: !!id,
  });
}

export function usePurchaseOrderPayments(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payments'],
    queryFn: () => purchaseOrdersApi.getPayments(id),
    enabled: !!id,
  });
}

export function useCreatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      purchaseOrdersApi.createPayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil dibuat');
    },
  });
}
```

### **Optional: Goods Receipt Hooks** (Only if you implement GR payments)

```typescript
// Only add these if you actually need goods receipt payments
export function useGoodsReceiptPaymentStatus(id: string) {
  return useQuery({
    queryKey: ['goods-receipts', id, 'payment-status'],
    queryFn: () => goodsReceiptsApi.getPaymentStatus(id),
    enabled: !!id,
  });
}

export function useConfirmDelivery() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      goodsReceiptsApi.confirmDelivery(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['goods-receipts'] });
      toast.success('Pengiriman berhasil dikonfirmasi');
    },
  });
}
```

---

## 🔒 **Simple Authentication** (Use your existing guards)

```typescript
// Just use your existing guards - no need to overcomplicate!
@UseGuards(JwtAuthGuard)           // For authentication
@UseGuards(ManagerGuard)           // For admin/pharmacist only actions
```

### **Role Access** (Keep it simple)
- **Admin/Pharmacist**: Can create, view, edit payments
- **Cashier**: Can view payments only
- **Others**: No payment access

---

## 📊 **Simple Error Handling** (Follow your existing patterns)

```typescript
// Use your existing error handling patterns
try {
  const payment = await this.createPayment(data);
  return payment;
} catch (error) {
  this.logger.error('Failed to create payment', error);
  throw new BadRequestException('Gagal membuat pembayaran');
}
```

---

## 🎯 **Key Recommendation: Start with Purchase Orders Only!**

### **Why Keep It Simple:**
1. **Your pharmacy is small** - complex payment workflows may be overkill
2. **Purchase orders are the main payment trigger** - most payments happen when you order from suppliers
3. **You already have payment infrastructure** - PaymentTermsService, SupplierPayment table
4. **Easy to extend later** - if you need goods receipt payments, add them incrementally

### **Implementation Priority:**
1. ✅ **Start**: Add 3 simple endpoints to existing purchase-order.controller.ts
2. ✅ **Then**: Create simple payment UI components
3. ✅ **Later**: Add goods receipt payments only if actually needed
4. ❌ **Skip**: Complex unified payment dashboard (unless you have hundreds of suppliers)

### **Database Changes Needed:**
```sql
-- Minimal changes to your existing schema
ALTER TABLE supplier_payments
ADD COLUMN purchase_order_id VARCHAR(36),
ADD FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id);

-- That's it! Use your existing tables.
```
| `BUSINESS_RULE_VIOLATION` | Business rule violation | 422 |

---

**This simplified approach will give you 80% of the payment functionality with 20% of the complexity!**

**Perfect for an Indonesian pharmacy store - no over-engineering needed! 🎯**

## 🧪 Testing

### API Testing
- Unit tests for all endpoints
- Integration tests for workflows
- Performance tests for large datasets
- Security tests for authorization

### Hook Testing
- React Query hook testing
- Error state testing
- Loading state testing
- Cache invalidation testing

---

**Next**: [Component Documentation](../components/README.md) | [Testing Documentation](../testing/README.md)
