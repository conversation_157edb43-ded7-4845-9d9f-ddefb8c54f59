# 🎯 Phase 1: Frontend Payment Components (Backend Already Complete!)

## 🎯 Objective
Build simple frontend components to display and interact with your existing excellent payment backend. **No backend changes needed!**

## ✅ **Backend Status: COMPLETE!**

Your backend already includes:
- ✅ All payment endpoints in PurchaseOrderController
- ✅ Sophisticated PaymentTermsService with Indonesian business logic
- ✅ Complete payment database schema with proper relationships
- ✅ Payment status management (PENDING, PARTIAL, PAID, OVERDUE)
- ✅ Transaction integrity and business rule validation

## 📋 Frontend Tasks Overview

### Task 1: Create PaymentStatusBadge Component
**Duration**: 2-3 hours | **Priority**: High | **Complexity**: Low

#### Description
Create a simple badge component to display payment status using your existing PaymentStatus enum.

#### Deliverables
- PaymentStatusBadge component with Indonesian labels
- Consistent styling with your design system
- Support for all payment statuses

#### Technical Details
```typescript
// Simple payment status badge
interface PaymentStatusBadgeProps {
  status: 'PENDING' | 'PARTIAL' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  className?: string;
  showTooltip?: boolean;
}

export function PaymentStatusBadge({ status, className, showTooltip }: PaymentStatusBadgeProps) {
  const config = {
    PENDING: { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800' },
    PARTIAL: { label: 'Sebagian', className: 'bg-blue-100 text-blue-800' },
    PAID: { label: 'Lunas', className: 'bg-green-100 text-green-800' },
    OVERDUE: { label: 'Terlambat', className: 'bg-red-100 text-red-800' },
    CANCELLED: { label: 'Dibatalkan', className: 'bg-gray-100 text-gray-800' },
  }[status];

  return (
    <Badge className={cn(config.className, className)}>
      {config.label}
    </Badge>
  );
}
```

#### Files to Create
- `packages/frontend/src/components/purchase-orders/PaymentStatusBadge.tsx`

#### Acceptance Criteria
- [ ] Displays all payment statuses correctly
- [ ] Uses Indonesian labels
- [ ] Consistent with design system
- [ ] Optional tooltip support

---

### Task 2: Create PaymentSummaryCard Component
**Duration**: 3-4 hours | **Priority**: High | **Complexity**: Low

#### Description
Create a card component to display payment summary data from your existing payment-summary API endpoint.

#### Deliverables
- PaymentSummaryCard component
- Integration with existing payment-summary API
- Loading and error states
- Indonesian language interface

#### Technical Details
```typescript
// Uses your existing API response
interface PaymentSummaryCardProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  className?: string;
}

export function PaymentSummaryCard({ purchaseOrder, onViewPayments, onAddPayment }: PaymentSummaryCardProps) {
  const { data: paymentSummary, isLoading } = usePurchaseOrderPaymentSummary(purchaseOrder.id);

  if (isLoading) return <PaymentSummaryCardSkeleton />;
  if (!paymentSummary) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Pembayaran</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Total:</span>
            <span>{formatCurrency(paymentSummary.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Dibayar:</span>
            <span className="text-green-600">{formatCurrency(paymentSummary.totalPaid)}</span>
          </div>
          <div className="flex justify-between">
            <span>Sisa:</span>
            <span className="text-blue-600">{formatCurrency(paymentSummary.remainingAmount)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Status:</span>
            <PaymentStatusBadge status={purchaseOrder.paymentStatus} />
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          <Button variant="outline" onClick={onViewPayments} className="flex-1">
            <Eye className="h-4 w-4 mr-2" />
            Lihat Pembayaran
          </Button>
          <Button onClick={onAddPayment} className="flex-1">
            <Plus className="h-4 w-4 mr-2" />
            Tambah Pembayaran
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### Files to Create
- `packages/frontend/src/components/purchase-orders/PaymentSummaryCard.tsx`
- `packages/frontend/src/components/purchase-orders/PaymentSummaryCardSkeleton.tsx`

#### Acceptance Criteria
- [ ] Displays payment summary correctly
- [ ] Uses existing payment-summary API
- [ ] Loading skeleton implemented
- [ ] Error handling included
- [ ] Indonesian language used
- [ ] Action buttons functional

---

### Task 3: Create Simple PaymentModal Component
**Duration**: 4-6 hours | **Priority**: High | **Complexity**: Medium

#### Description
Create a basic payment modal with tabs for summary, payments list, and payment creation using your existing APIs.

#### Deliverables
- PaymentModal component with tabbed interface
- Integration with all existing payment APIs
- Payment creation form
- Payment list display

#### Technical Details
```typescript
// Simple payment modal using your existing APIs
interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrderWithRelations;
  initialTab?: 'summary' | 'payments' | 'add';
}

export function PaymentModal({ open, onOpenChange, purchaseOrder, initialTab = 'summary' }: PaymentModalProps) {
  const { data: paymentSummary } = usePurchaseOrderPaymentSummary(purchaseOrder.id);
  const { data: payments } = usePurchaseOrderPayments(purchaseOrder.id);
  const createPayment = useCreatePurchaseOrderPayment();

  const handleSubmit = (data: CreateSupplierPaymentDto) => {
    createPayment.mutate({ id: purchaseOrder.id, data }, {
      onSuccess: () => {
        onOpenChange(false);
        toast.success('Pembayaran berhasil dibuat');
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Kelola Pembayaran - {purchaseOrder.orderNumber}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue={initialTab}>
          <TabsList>
            <TabsTrigger value="summary">Ringkasan</TabsTrigger>
            <TabsTrigger value="payments">Riwayat</TabsTrigger>
            <TabsTrigger value="add">Tambah</TabsTrigger>
          </TabsList>

          <TabsContent value="summary">
            <PaymentSummaryCard
              purchaseOrder={purchaseOrder}
              onViewPayments={() => {/* switch to payments tab */}}
              onAddPayment={() => {/* switch to add tab */}}
            />
          </TabsContent>

          <TabsContent value="payments">
            <PaymentList payments={payments} />
          </TabsContent>

          <TabsContent value="add">
            <PaymentForm onSubmit={handleSubmit} isLoading={createPayment.isPending} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
```

#### Files to Create
- `packages/frontend/src/components/purchase-orders/PaymentModal.tsx`
- `packages/frontend/src/components/purchase-orders/PaymentList.tsx`
- `packages/frontend/src/components/purchase-orders/PaymentForm.tsx`

#### Acceptance Criteria
- [ ] Modal opens and closes correctly
- [ ] All tabs functional
- [ ] Uses existing payment APIs
- [ ] Payment creation works
- [ ] Error handling implemented
- [ ] Indonesian language used

---

### Task 4: Create React Query Payment Hooks
**Duration**: 2-3 hours | **Priority**: High | **Complexity**: Low

#### Description
Create React Query hooks to call your existing payment APIs with proper error handling and caching.

#### Deliverables
- Payment summary hook
- Payment list hook
- Payment creation mutation
- Payment update/delete mutations

#### Technical Details
```typescript
// React Query hooks for your existing APIs
export function usePurchaseOrderPaymentSummary(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payment-summary'],
    queryFn: () => purchaseOrdersApi.getPaymentSummary(id), // Uses your existing endpoint
    enabled: !!id,
  });
}

export function usePurchaseOrderPayments(id: string, params?: any) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payments', params],
    queryFn: () => purchaseOrdersApi.getPayments(id, params), // Uses your existing endpoint
    enabled: !!id,
  });
}

export function useCreatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateSupplierPaymentDto }) =>
      purchaseOrdersApi.createPayment(id, data), // Uses your existing endpoint
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil dibuat');
    },
    onError: (error) => {
      toast.error('Gagal membuat pembayaran');
      console.error('Payment creation error:', error);
    },
  });
}

export function useUpdatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, paymentId, data }: { id: string; paymentId: string; data: Partial<CreateSupplierPaymentDto> }) =>
      purchaseOrdersApi.updatePayment(id, paymentId, data), // Uses your existing endpoint
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil diperbarui');
    },
  });
}
```

#### Files to Create
- `packages/frontend/src/hooks/usePurchaseOrderPayments.ts`

#### Acceptance Criteria
- [ ] All hooks properly typed
- [ ] Error handling implemented
- [ ] Cache invalidation working
- [ ] Toast notifications functional
- [ ] Follows existing hook patterns

---

### Task 5: Add Payment Column to Purchase Order Table
**Duration**: 1-2 hours | **Priority**: High | **Complexity**: Low

#### Description
Add payment status column and payment action to existing purchase order table.

#### Deliverables
- Payment status column in purchase order table
- Payment action in dropdown menu
- Integration with PaymentModal

#### Technical Details
```typescript
// Add to your existing purchase order columns
{
  accessorKey: 'paymentStatus',
  header: 'Status Pembayaran',
  cell: ({ row }) => {
    const po = row.original;
    return <PaymentStatusBadge status={po.paymentStatus || 'PENDING'} />;
  },
}

// Add to your existing purchase order actions dropdown
<DropdownMenuItem onClick={() => openPaymentModal(purchaseOrder)}>
  <CreditCard className="mr-2 h-4 w-4" />
  Kelola Pembayaran
</DropdownMenuItem>
```

#### Files to Modify
- `packages/frontend/src/app/(dashboard)/dashboard/purchase-orders/columns.tsx`
- `packages/frontend/src/app/(dashboard)/dashboard/purchase-orders/page.tsx`

#### Acceptance Criteria
- [ ] Payment column displays correctly
- [ ] Payment action opens modal
- [ ] Consistent with existing table patterns
- [ ] Responsive design maintained

## 🔧 Technical Implementation Guide

### Development Setup
1. Your backend is already complete - no setup needed!
2. Run type checking: `bun run type-check`
3. Start development server: `bun run dev`
4. Run tests: `bun run test`

### Code Standards
- Use TypeScript strict mode
- Follow your existing component patterns
- Implement proper error boundaries
- Add comprehensive prop validation
- Include loading and error states
- Use Indonesian language for UI text

### Testing Requirements
- Unit tests for all components
- Integration tests with existing APIs
- E2E tests for payment workflows
- Test with your existing payment data

## 📊 Success Criteria

### Functional Requirements
- [ ] PaymentStatusBadge displays correctly
- [ ] PaymentSummaryCard shows API data
- [ ] PaymentModal integrates with existing APIs
- [ ] React Query hooks work properly
- [ ] Payment table column functional

### Non-Functional Requirements
- [ ] Performance: Uses existing API performance
- [ ] Accessibility: WCAG 2.1 AA compliant
- [ ] Browser support: Chrome, Firefox, Safari
- [ ] Mobile responsive design

### Business Requirements
- [ ] Indonesian language interface
- [ ] Uses existing payment business logic
- [ ] Integrates with existing workflow
- [ ] Error handling comprehensive

## 🚀 Deployment Checklist

- [ ] All tests passing
- [ ] Type checking clean
- [ ] Code review completed
- [ ] Components integrate with existing APIs
- [ ] Indonesian language verified
- [ ] Responsive design tested

---

**Estimated Total Time: 1-2 days (frontend components only!)**

**Next Phase**: [Phase 2: Optional Goods Receipt Payment UI](../phase-2/README.md)
