# 🧩 Payment Integration Component Documentation

## 🎯 Overview
Simple frontend components to display and interact with your existing excellent payment backend. **Your backend is already complete - we just need UI components!**

## ✅ **Backend Status: COMPLETE!**

Your backend already provides:
- ✅ All payment endpoints implemented in PurchaseOrderController and GoodsReceiptController
- ✅ Sophisticated PaymentTermsService with Indonesian business logic
- ✅ Complete payment database schema with proper relationships
- ✅ Payment status management (PENDING, PARTIAL, PAID, OVERDUE)
- ✅ Transaction integrity and business rule validation

## 📋 Simple Component Categories

### Core Payment Components (Essential)
- PaymentStatusBadge - Display payment status with Indonesian labels
- PaymentSummaryCard - Show payment summary from your existing API
- PaymentModal - Simple modal for payment management
- Payment hooks - React Query hooks for your existing APIs

### Optional Components (Skip for Most Pharmacies)
- Goods receipt payment components (only if needed)
- Payment dashboard components (only for large operations)
- Advanced analytics (skip unless 50+ suppliers)

---

## 🔧 Essential Payment Components

### PaymentStatusBadge

**Purpose**: Display payment status with Indonesian labels using your existing PaymentStatus enum

```typescript
interface PaymentStatusBadgeProps {
  status: 'PENDING' | 'PARTIAL' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  className?: string;
  showTooltip?: boolean;
}
```

**Implementation:**
```tsx
export function PaymentStatusBadge({ status, className, showTooltip }: PaymentStatusBadgeProps) {
  const config = {
    PENDING: { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800' },
    PARTIAL: { label: 'Sebagian', className: 'bg-blue-100 text-blue-800' },
    PAID: { label: 'Lunas', className: 'bg-green-100 text-green-800' },
    OVERDUE: { label: 'Terlambat', className: 'bg-red-100 text-red-800' },
    CANCELLED: { label: 'Dibatalkan', className: 'bg-gray-100 text-gray-800' },
  }[status];

  return (
    <Badge className={cn(config.className, className)}>
      {config.label}
    </Badge>
  );
}
```

**Usage:**
```tsx
<PaymentStatusBadge status="PARTIAL" showTooltip={true} />
```

**Features:**
- Indonesian language labels
- Consistent with your design system
- Simple and focused
- Optional tooltip support

---

### PaymentSummaryCard

**Purpose**: Display payment summary data from your existing payment-summary API endpoint

```typescript
interface PaymentSummaryCardProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  className?: string;
}
```

**Implementation:**
```tsx
export function PaymentSummaryCard({ purchaseOrder, onViewPayments, onAddPayment }: PaymentSummaryCardProps) {
  const { data: paymentSummary, isLoading } = usePurchaseOrderPaymentSummary(purchaseOrder.id);

  if (isLoading) return <PaymentSummaryCardSkeleton />;
  if (!paymentSummary) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Pembayaran</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Total:</span>
            <span>{formatCurrency(paymentSummary.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Dibayar:</span>
            <span className="text-green-600">{formatCurrency(paymentSummary.totalPaid)}</span>
          </div>
          <div className="flex justify-between">
            <span>Sisa:</span>
            <span className="text-blue-600">{formatCurrency(paymentSummary.remainingAmount)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Status:</span>
            <PaymentStatusBadge status={purchaseOrder.paymentStatus} />
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          <Button variant="outline" onClick={onViewPayments} className="flex-1">
            <Eye className="h-4 w-4 mr-2" />
            Lihat Pembayaran
          </Button>
          <Button onClick={onAddPayment} className="flex-1">
            <Plus className="h-4 w-4 mr-2" />
            Tambah Pembayaran
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

**Features:**
- Uses your existing payment-summary API
- Indonesian language interface
- Loading skeleton states
- Action buttons for payment management
- Consistent with your design system

---

### PaymentModal

**Purpose**: Simple modal for payment management using your existing APIs

```typescript
interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrderWithRelations;
  initialTab?: 'summary' | 'payments' | 'add';
}
```

**Implementation:**
```tsx
export function PaymentModal({ open, onOpenChange, purchaseOrder, initialTab = 'summary' }: PaymentModalProps) {
  const { data: paymentSummary } = usePurchaseOrderPaymentSummary(purchaseOrder.id);
  const { data: payments } = usePurchaseOrderPayments(purchaseOrder.id);
  const createPayment = useCreatePurchaseOrderPayment();

  const handleSubmit = (data: CreateSupplierPaymentDto) => {
    createPayment.mutate({ id: purchaseOrder.id, data }, {
      onSuccess: () => {
        onOpenChange(false);
        toast.success('Pembayaran berhasil dibuat');
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Kelola Pembayaran - {purchaseOrder.orderNumber}</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue={initialTab}>
          <TabsList>
            <TabsTrigger value="summary">Ringkasan</TabsTrigger>
            <TabsTrigger value="payments">Riwayat</TabsTrigger>
            <TabsTrigger value="add">Tambah</TabsTrigger>
          </TabsList>

          <TabsContent value="summary">
            <PaymentSummaryCard purchaseOrder={purchaseOrder} />
          </TabsContent>

          <TabsContent value="payments">
            <PaymentList payments={payments} />
          </TabsContent>

          <TabsContent value="add">
            <PaymentForm onSubmit={handleSubmit} isLoading={createPayment.isPending} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
```

**Features:**
- Tabbed interface for different payment operations
- Uses all your existing payment APIs
- Payment creation with proper error handling
- Indonesian language interface
- Consistent with your modal patterns

---

## 🔗 React Query Payment Hooks

### Purchase Order Payment Hooks

**Purpose**: Call your existing payment APIs with proper error handling and caching

```typescript
// Payment summary hook
export function usePurchaseOrderPaymentSummary(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payment-summary'],
    queryFn: () => purchaseOrdersApi.getPaymentSummary(id), // Uses your existing endpoint
    enabled: !!id,
  });
}

// Payment list hook
export function usePurchaseOrderPayments(id: string, params?: any) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payments', params],
    queryFn: () => purchaseOrdersApi.getPayments(id, params), // Uses your existing endpoint
    enabled: !!id,
  });
}

// Payment creation mutation
export function useCreatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateSupplierPaymentDto }) =>
      purchaseOrdersApi.createPayment(id, data), // Uses your existing endpoint
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil dibuat');
    },
    onError: (error) => {
      toast.error('Gagal membuat pembayaran');
      console.error('Payment creation error:', error);
    },
  });
}

// Payment update mutation
export function useUpdatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, paymentId, data }: { id: string; paymentId: string; data: Partial<CreateSupplierPaymentDto> }) =>
      purchaseOrdersApi.updatePayment(id, paymentId, data), // Uses your existing endpoint
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil diperbarui');
    },
  });
}
```

**Features:**
- Uses all your existing payment APIs
- Proper error handling with Indonesian messages
- Cache invalidation for real-time updates
- Follows your existing hook patterns
- Toast notifications for user feedback

---

## 📋 Table Integration

### Purchase Order Table Payment Column

**Purpose**: Add payment status column to existing purchase order table

```typescript
// Add to your existing purchase order columns
{
  accessorKey: 'paymentStatus',
  header: 'Status Pembayaran',
  cell: ({ row }) => {
    const po = row.original;
    return <PaymentStatusBadge status={po.paymentStatus || 'PENDING'} />;
  },
}
```

### Purchase Order Actions Dropdown

**Purpose**: Add payment action to existing dropdown menu

```typescript
// Add to your existing purchase order actions dropdown
<DropdownMenuItem onClick={() => openPaymentModal(purchaseOrder)}>
  <CreditCard className="mr-2 h-4 w-4" />
  Kelola Pembayaran
</DropdownMenuItem>
```

**Features:**
- Consistent with existing table patterns
- Uses existing PaymentStatusBadge component
- Integrates with existing dropdown actions
- Opens PaymentModal for payment management

---

## 🎯 **Optional: Goods Receipt Payment Components**

### GoodsReceiptPaymentSummary (Optional)

**Purpose**: Payment summary for goods receipts (only if needed)

```typescript
interface GoodsReceiptPaymentSummaryProps {
  goodsReceipt: GoodsReceiptWithRelations;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
}
```

**Implementation:**
```tsx
export function GoodsReceiptPaymentSummary({ goodsReceipt, onViewPayments, onAddPayment }: GoodsReceiptPaymentSummaryProps) {
  const { data: paymentSummary } = useGoodsReceiptPaymentSummary(goodsReceipt.id);

  if (!paymentSummary) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Pembayaran Goods Receipt</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Similar to PaymentSummaryCard but for goods receipts */}
      </CardContent>
    </Card>
  );
}
```

**Note**: Most small pharmacies can skip goods receipt payment components and focus on purchase order payments only.

---

## 🎯 **Implementation Priority**

### **Essential Components (Implement First - 1-2 days):**

1. **PaymentStatusBadge** (2-3 hours)
   - Simple badge with Indonesian labels
   - Uses your existing PaymentStatus enum
   - Consistent with design system

2. **PaymentSummaryCard** (3-4 hours)
   - Displays data from your existing payment-summary API
   - Loading states and error handling
   - Action buttons for payment management

3. **PaymentModal** (4-6 hours)
   - Tabbed interface for payment operations
   - Uses all your existing payment APIs
   - Payment creation and viewing

4. **React Query Hooks** (2-3 hours)
   - Hooks for your existing payment APIs
   - Proper error handling and caching
   - Follows your existing patterns

5. **Table Integration** (1-2 hours)
   - Payment column in purchase order table
   - Payment action in dropdown menu
   - Consistent with existing patterns

### **Optional Components (Skip for Most Pharmacies):**

- **Goods Receipt Payment Components** (2-3 hours if needed)
  - Only implement if you need delivery-based payments
  - Your backend already supports it

- **Payment Dashboard** (4-6 hours if needed)
  - Only for large operations with 50+ suppliers
  - Simple analytics and overview

### **Skip Entirely (Unless Large Operation):**
- Complex unified payment systems
- Advanced analytics and reporting
- Workflow automation
- Compliance monitoring dashboards

---

## 🎨 Design System Integration

### Payment Status Colors (Use Your Existing Design System)
```typescript
// Use your existing Tailwind classes
const paymentStatusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  PARTIAL: 'bg-blue-100 text-blue-800',
  PAID: 'bg-green-100 text-green-800',
  OVERDUE: 'bg-red-100 text-red-800',
  CANCELLED: 'bg-gray-100 text-gray-800',
};
```

### Indonesian Language Labels
```typescript
const paymentStatusLabels = {
  PENDING: 'Menunggu',
  PARTIAL: 'Sebagian',
  PAID: 'Lunas',
  OVERDUE: 'Terlambat',
  CANCELLED: 'Dibatalkan',
};
```

### Icons (Use Your Existing Icon Library)
- **Payment**: CreditCard, DollarSign
- **Actions**: Plus, Eye, Edit, Trash2
- **Status**: CheckCircle, XCircle, Clock, AlertTriangle

---

## 🧪 Simple Component Testing

### Testing Strategy
```typescript
// Simple component testing example
describe('PaymentStatusBadge', () => {
  it('menampilkan label status yang benar', () => {
    render(<PaymentStatusBadge status="PAID" />);
    expect(screen.getByText('Lunas')).toBeInTheDocument();
  });

  it('menerapkan styling yang benar untuk status overdue', () => {
    render(<PaymentStatusBadge status="OVERDUE" />);
    expect(screen.getByText('Terlambat')).toHaveClass('bg-red-100');
  });

  it('menggunakan API yang sudah ada', async () => {
    const { result } = renderHook(() => usePurchaseOrderPaymentSummary('po-1'));
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });
  });
});
```

### Testing Requirements
- [ ] Unit tests for all components
- [ ] Integration tests with existing APIs
- [ ] Indonesian language testing
- [ ] Responsive design testing

---

## 📊 **Success Criteria**

### **Functional Requirements**
- [ ] PaymentStatusBadge displays correctly with Indonesian labels
- [ ] PaymentSummaryCard shows data from existing payment-summary API
- [ ] PaymentModal integrates with all existing payment APIs
- [ ] React Query hooks work with existing endpoints
- [ ] Table integration follows existing patterns

### **Technical Requirements**
- [ ] Uses existing backend APIs (no backend changes)
- [ ] Follows existing component patterns
- [ ] Indonesian language interface
- [ ] Consistent with design system
- [ ] Proper error handling

### **Business Requirements**
- [ ] Supports Indonesian pharmacy workflow
- [ ] Simple and not over-engineered
- [ ] Can be implemented in 1-2 days
- [ ] Uses existing payment business logic

---

## 🎯 **Final Recommendation**

**Your backend payment system is already enterprise-grade!**

**All you need:**
- ✅ 5 simple frontend components (1-2 days)
- ✅ Uses your existing excellent APIs
- ✅ Indonesian pharmacy-focused interface
- ✅ Simple, maintainable, and focused

**Perfect for Indonesian pharmacy store - no complexity needed!** 🚀

---

**Next**: [Start Implementation with PaymentStatusBadge](../phase-1/README.md)
