# 🧪 Payment Integration Testing Documentation

## 🎯 Overview
Comprehensive testing strategy and documentation for the payment management integration across purchase orders and goods receipts.

## 📋 Testing Strategy

### Testing Pyramid
```
    🔺 E2E Tests (10%)
   🔺🔺 Integration Tests (20%)
  🔺🔺🔺 Unit Tests (70%)
```

### Testing Frameworks
- **Unit Tests**: Vitest + React Testing Library
- **Integration Tests**: Vitest + MSW (Mock Service Worker)
- **E2E Tests**: Cypress 13.6.3+
- **Component Tests**: Cypress Component Testing

---

## 🔬 Unit Testing

### Component Testing

#### PaymentStatusBadge Tests
```typescript
// packages/frontend/src/components/purchase-orders/__tests__/PaymentStatusBadge.test.tsx
import { render, screen } from '@testing-library/react';
import { PaymentStatusBadge } from '../PaymentStatusBadge';

describe('PaymentStatusBadge', () => {
  it('menampilkan label status yang benar untuk setiap status', () => {
    const statuses = [
      { status: 'PENDING', label: '<PERSON>unggu' },
      { status: 'PARTIAL', label: 'Sebagian' },
      { status: 'PAID', label: 'Lunas' },
      { status: 'OVERDUE', label: 'Terlambat' },
      { status: 'CANCELLED', label: 'Dibatalkan' },
    ];

    statuses.forEach(({ status, label }) => {
      render(<PaymentStatusBadge status={status as any} />);
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it('menerapkan styling yang benar untuk status overdue', () => {
    render(<PaymentStatusBadge status="OVERDUE" />);
    const badge = screen.getByText('Terlambat');
    expect(badge).toHaveClass('bg-red-100', 'text-red-800');
  });

  it('menampilkan tooltip ketika diaktifkan', async () => {
    const { user } = renderWithUser(
      <PaymentStatusBadge status="PARTIAL" showTooltip={true} />
    );
    
    const badge = screen.getByText('Sebagian');
    await user.hover(badge);
    
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });
  });
});
```

#### PaymentSummaryCard Tests
```typescript
// packages/frontend/src/components/purchase-orders/__tests__/PaymentSummaryCard.test.tsx
describe('PaymentSummaryCard', () => {
  const mockPaymentSummary = {
    purchaseOrderId: 'po-1',
    totalAmount: 1000000,
    paidAmount: 300000,
    remainingAmount: 700000,
    paymentStatus: 'PARTIAL',
    paymentCount: 1,
    overdueAmount: 0,
    isOverdue: false,
  };

  it('menampilkan informasi pembayaran dengan benar', () => {
    render(
      <PaymentSummaryCard 
        paymentSummary={mockPaymentSummary}
        onViewPayments={jest.fn()}
        onAddPayment={jest.fn()}
      />
    );

    expect(screen.getByText('Rp 1.000.000')).toBeInTheDocument();
    expect(screen.getByText('Rp 300.000')).toBeInTheDocument();
    expect(screen.getByText('Rp 700.000')).toBeInTheDocument();
  });

  it('menampilkan peringatan untuk pembayaran terlambat', () => {
    const overduePaymentSummary = {
      ...mockPaymentSummary,
      isOverdue: true,
      overdueAmount: 200000,
    };

    render(<PaymentSummaryCard paymentSummary={overduePaymentSummary} />);
    
    expect(screen.getByText(/Pembayaran Terlambat/)).toBeInTheDocument();
    expect(screen.getByText(/Rp 200.000/)).toBeInTheDocument();
  });

  it('memanggil callback ketika tombol aksi diklik', async () => {
    const onViewPayments = jest.fn();
    const onAddPayment = jest.fn();
    const { user } = renderWithUser(
      <PaymentSummaryCard 
        paymentSummary={mockPaymentSummary}
        onViewPayments={onViewPayments}
        onAddPayment={onAddPayment}
      />
    );

    await user.click(screen.getByText('Lihat Pembayaran'));
    expect(onViewPayments).toHaveBeenCalled();

    await user.click(screen.getByText('Tambah Pembayaran'));
    expect(onAddPayment).toHaveBeenCalled();
  });
});
```

### Hook Testing

#### usePurchaseOrderPayments Tests
```typescript
// packages/frontend/src/hooks/__tests__/usePurchaseOrders.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { usePurchaseOrderPaymentSummary } from '../usePurchaseOrders';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('usePurchaseOrderPaymentSummary', () => {
  it('mengambil data payment summary dengan benar', async () => {
    const { result } = renderHook(
      () => usePurchaseOrderPaymentSummary('po-1'),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(
      expect.objectContaining({
        purchaseOrderId: 'po-1',
        totalAmount: expect.any(Number),
        paymentStatus: expect.any(String),
      })
    );
  });

  it('menangani error dengan benar', async () => {
    // Mock API error
    server.use(
      rest.get('/api/purchase-orders/invalid/payment-summary', (req, res, ctx) => {
        return res(ctx.status(404), ctx.json({ error: 'Not found' }));
      })
    );

    const { result } = renderHook(
      () => usePurchaseOrderPaymentSummary('invalid'),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });
  });
});
```

### Utility Function Testing

#### Delivery Payment Calculator Tests
```typescript
// packages/frontend/src/lib/utils/__tests__/delivery-payment-calculator.test.ts
import { calculateDueDate, adjustForBusinessDays } from '../delivery-payment-calculator';

describe('DeliveryPaymentCalculator', () => {
  describe('calculateDueDate', () => {
    it('menghitung due date dengan benar berdasarkan tanggal pengiriman', () => {
      const deliveryDate = '2024-01-15';
      const paymentTerms = 30;
      
      const dueDate = calculateDueDate(deliveryDate, paymentTerms);
      
      expect(dueDate).toBe('2024-02-14');
    });

    it('menyesuaikan untuk hari kerja Indonesia', () => {
      // Delivery on Friday, 30 days = Sunday, should adjust to Monday
      const deliveryDate = '2024-01-12'; // Friday
      const paymentTerms = 30;
      
      const dueDate = calculateDueDate(deliveryDate, paymentTerms);
      const adjustedDate = adjustForBusinessDays(dueDate);
      
      // Should be adjusted to next business day
      expect(new Date(adjustedDate).getDay()).not.toBe(0); // Not Sunday
    });

    it('mengecualikan hari libur nasional Indonesia', () => {
      const deliveryDate = '2024-08-01'; // Before Independence Day
      const paymentTerms = 17; // Would land on Independence Day (Aug 17)
      
      const dueDate = calculateDueDate(deliveryDate, paymentTerms);
      
      // Should be adjusted past the holiday
      expect(dueDate).not.toBe('2024-08-17');
    });
  });
});
```

---

## 🔗 Integration Testing

### API Integration Tests

#### Purchase Order Payment API Tests
```typescript
// packages/frontend/src/lib/api/__tests__/purchase-orders.test.ts
import { server } from '../../../test/server';
import { purchaseOrdersApi } from '../purchase-orders';

describe('Purchase Order Payment API', () => {
  it('membuat pembayaran purchase order dengan benar', async () => {
    const paymentData = {
      purchaseOrderId: 'po-1',
      amount: 500000,
      paymentMethod: 'TRANSFER',
      paymentDate: '2024-01-15',
      reference: 'TRF-001',
    };

    const result = await purchaseOrdersApi.createPurchaseOrderPayment('po-1', paymentData);

    expect(result).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        amount: 500000,
        paymentMethod: 'TRANSFER',
        status: 'PENDING',
      })
    );
  });

  it('menangani error validasi dengan benar', async () => {
    const invalidPaymentData = {
      purchaseOrderId: 'po-1',
      amount: -100, // Invalid amount
      paymentMethod: 'INVALID',
      paymentDate: 'invalid-date',
    };

    await expect(
      purchaseOrdersApi.createPurchaseOrderPayment('po-1', invalidPaymentData as any)
    ).rejects.toThrow();
  });
});
```

### Component Integration Tests

#### Payment Modal Integration Tests
```typescript
// packages/frontend/src/components/purchase-orders/__tests__/PurchaseOrderPaymentModal.integration.test.tsx
describe('PurchaseOrderPaymentModal Integration', () => {
  it('menampilkan dan mengelola pembayaran dengan lengkap', async () => {
    const mockPurchaseOrder = createMockPurchaseOrder();
    const { user } = renderWithProviders(
      <PurchaseOrderPaymentModal
        open={true}
        onOpenChange={jest.fn()}
        purchaseOrder={mockPurchaseOrder}
      />
    );

    // Test tab navigation
    await user.click(screen.getByText('Riwayat Pembayaran'));
    expect(screen.getByText('Daftar semua pembayaran')).toBeInTheDocument();

    // Test payment creation
    await user.click(screen.getByText('Tambah Pembayaran'));
    await user.click(screen.getByText('Tambah Pembayaran Baru'));
    
    // Should show payment form
    expect(screen.getByText('Masukkan detail pembayaran')).toBeInTheDocument();
  });

  it('menangani workflow pembayaran advance dengan benar', async () => {
    const mockPurchaseOrder = createMockPurchaseOrder();
    const { user } = renderWithProviders(
      <PurchaseOrderPaymentModal
        open={true}
        onOpenChange={jest.fn()}
        purchaseOrder={mockPurchaseOrder}
        initialTab="add"
        initialAction="add"
      />
    );

    // Should start on add payment tab
    expect(screen.getByText('Tambah Pembayaran Baru')).toBeInTheDocument();
    
    // Test advance payment workflow
    await user.click(screen.getByText('Pembayaran Advance'));
    expect(screen.getByText('Pilih persentase advance')).toBeInTheDocument();
  });
});
```

---

## 🎭 End-to-End Testing

### Purchase Order Payment E2E Tests

```typescript
// cypress/e2e/purchase-order-payments.cy.ts
describe('Purchase Order Payment Management', () => {
  beforeEach(() => {
    cy.login('admin');
    cy.visit('/dashboard/purchase-orders');
  });

  it('mengelola pembayaran purchase order dari awal hingga akhir', () => {
    // Create purchase order
    cy.get('[data-testid="create-purchase-order"]').click();
    cy.fillPurchaseOrderForm({
      supplier: 'PT Supplier Test',
      paymentTerms: 30,
      paymentMethod: 'TRANSFER',
    });
    cy.get('[data-testid="submit-purchase-order"]').click();

    // Navigate to payment management
    cy.get('[data-testid="purchase-order-row"]').first().within(() => {
      cy.get('[data-testid="payment-actions"]').click();
      cy.get('[data-testid="view-payment-summary"]').click();
    });

    // Verify payment modal opens
    cy.get('[data-testid="purchase-order-payment-modal"]').should('be.visible');
    cy.get('[data-testid="payment-summary-tab"]').should('be.visible');

    // Add payment
    cy.get('[data-testid="add-payment-button"]').click();
    cy.get('[data-testid="payment-amount"]').type('500000');
    cy.get('[data-testid="payment-method"]').select('TRANSFER');
    cy.get('[data-testid="payment-reference"]').type('TRF-001');
    cy.get('[data-testid="submit-payment"]').click();

    // Verify payment created
    cy.get('[data-testid="payment-success-message"]').should('contain', 'Pembayaran berhasil dibuat');
    cy.get('[data-testid="payment-list"]').should('contain', 'Rp 500.000');
  });

  it('menangani pembayaran advance dengan benar', () => {
    cy.visit('/dashboard/purchase-orders/po-1');
    
    // Open payment management
    cy.get('[data-testid="manage-payments"]').click();
    cy.get('[data-testid="add-payment-tab"]').click();

    // Select advance payment
    cy.get('[data-testid="advance-payment-option"]').click();
    cy.get('[data-testid="advance-percentage-30"]').click();

    // Verify calculation
    cy.get('[data-testid="advance-amount"]').should('contain', 'Rp 300.000');
    cy.get('[data-testid="remaining-amount"]').should('contain', 'Rp 700.000');

    // Create advance payment
    cy.get('[data-testid="create-advance-payment"]').click();
    cy.get('[data-testid="payment-success-message"]').should('be.visible');
  });
});
```

### Goods Receipt Payment E2E Tests

```typescript
// cypress/e2e/goods-receipt-payments.cy.ts
describe('Goods Receipt Payment Management', () => {
  beforeEach(() => {
    cy.login('admin');
    cy.createTestPurchaseOrder().then((poId) => {
      cy.createTestGoodsReceipt(poId);
    });
  });

  it('memproses pembayaran berdasarkan pengiriman', () => {
    cy.visit('/dashboard/goods-receipts');
    
    // Open goods receipt detail
    cy.get('[data-testid="goods-receipt-row"]').first().click();
    
    // Confirm delivery
    cy.get('[data-testid="confirm-delivery"]').click();
    cy.get('[data-testid="delivery-date"]').type('2024-01-15');
    cy.get('[data-testid="confirm-delivery-submit"]').click();

    // Verify due date recalculation
    cy.get('[data-testid="calculated-due-date"]').should('contain', '2024-02-14');

    // Trigger payment
    cy.get('[data-testid="trigger-payment"]').click();
    cy.get('[data-testid="payment-amount"]').should('have.value', '1000000');
    cy.get('[data-testid="submit-payment"]').click();

    // Verify payment created
    cy.get('[data-testid="payment-success-message"]').should('be.visible');
  });

  it('mencocokkan invoice dengan goods receipt', () => {
    cy.visit('/dashboard/goods-receipts/gr-1');
    
    // Open invoice matching
    cy.get('[data-testid="match-invoice"]').click();
    
    // Upload invoice
    cy.get('[data-testid="invoice-upload"]').selectFile('cypress/fixtures/invoice.pdf');
    
    // Fill invoice details
    cy.get('[data-testid="invoice-number"]').type('INV-001');
    cy.get('[data-testid="invoice-date"]').type('2024-01-15');
    cy.get('[data-testid="invoice-amount"]').type('1000000');
    
    // Submit matching
    cy.get('[data-testid="submit-invoice-match"]').click();
    
    // Verify matching result
    cy.get('[data-testid="matching-status"]').should('contain', 'MATCHED');
    cy.get('[data-testid="invoice-matched-badge"]').should('be.visible');
  });
});
```

---

## 📊 Performance Testing

### Load Testing

```typescript
// packages/frontend/src/__tests__/performance/payment-dashboard.test.ts
describe('Payment Dashboard Performance', () => {
  it('memuat dashboard dengan 1000+ pembayaran dalam waktu yang wajar', async () => {
    const startTime = performance.now();
    
    render(<PaymentDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('payment-dashboard')).toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    expect(loadTime).toBeLessThan(3000); // Should load in less than 3 seconds
  });

  it('menangani scroll virtual untuk daftar pembayaran besar', async () => {
    const largePaymentList = Array.from({ length: 10000 }, (_, i) => 
      createMockPayment({ id: `payment-${i}` })
    );

    render(<PaymentList payments={largePaymentList} />);
    
    // Should only render visible items
    const renderedItems = screen.getAllByTestId(/payment-item-/);
    expect(renderedItems.length).toBeLessThan(100); // Virtual scrolling
  });
});
```

---

## 🔒 Security Testing

### Authentication Tests

```typescript
// cypress/e2e/security/payment-security.cy.ts
describe('Payment Security', () => {
  it('mencegah akses tidak sah ke pembayaran', () => {
    cy.login('staff'); // Limited permissions
    
    cy.visit('/dashboard/purchase-orders/po-1');
    
    // Should not see payment management buttons
    cy.get('[data-testid="add-payment"]').should('not.exist');
    cy.get('[data-testid="delete-payment"]').should('not.exist');
    
    // Should only see view permissions
    cy.get('[data-testid="view-payment-summary"]').should('exist');
  });

  it('memvalidasi input pembayaran untuk mencegah injection', () => {
    cy.login('admin');
    cy.visit('/dashboard/purchase-orders/po-1');
    
    cy.get('[data-testid="add-payment"]').click();
    
    // Try SQL injection
    cy.get('[data-testid="payment-reference"]').type("'; DROP TABLE payments; --");
    cy.get('[data-testid="submit-payment"]').click();
    
    // Should show validation error
    cy.get('[data-testid="validation-error"]').should('contain', 'Invalid characters');
  });
});
```

---

## 📋 Test Coverage Requirements

### Minimum Coverage Targets
- **Unit Tests**: 90% line coverage
- **Integration Tests**: 80% feature coverage
- **E2E Tests**: 100% critical path coverage

### Coverage Reports
```bash
# Generate coverage report
bun run test:coverage

# View coverage report
open coverage/index.html
```

---

## 🚀 Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/payment-integration-tests.yml
name: Payment Integration Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: bun install
      
      - name: Run unit tests
        run: bun run test:unit
      
      - name: Run integration tests
        run: bun run test:integration
      
      - name: Run E2E tests
        run: bun run test:e2e:ci
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

---

**Next**: [Deployment Guide](../deployment/README.md)
