import { apiClient } from '../axios';
import {
  PurchaseOrderWithRelations,
  PurchaseOrderQueryParams,
  PurchaseOrderListResponse,
  PurchaseOrderStatsResponse,
  CreatePurchaseOrderDto,
  UpdatePurchaseOrderDto,
  PurchaseOrderStatusUpdateDto,
  PurchaseOrderPaymentSummary,
  PurchaseOrderPaymentInfo,
  CreatePurchaseOrderPaymentDto,
  PurchaseOrderPaymentQueryParams,
} from '@/types/purchase-order';
import {
  SupplierPaymentWithRelations,
  PaymentSchedule,
  PaymentListResponse,
} from '@/types/supplier';

export const purchaseOrdersApi = {
  // Get all purchase orders with filtering and pagination
  getPurchaseOrders: async (params: PurchaseOrderQueryParams = {}): Promise<PurchaseOrderListResponse> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/purchase-orders?${searchParams.toString()}`);
    return response.data;
  },

  // Get a specific purchase order by ID
  getPurchaseOrder: async (id: string): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.get(`/purchase-orders/${id}`);
    return response.data;
  },

  // Create a new purchase order
  createPurchaseOrder: async (data: CreatePurchaseOrderDto): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.post('/purchase-orders', data);
    return response.data;
  },

  // Update an existing purchase order
  updatePurchaseOrder: async (id: string, data: UpdatePurchaseOrderDto): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.patch(`/purchase-orders/${id}`, data);
    return response.data;
  },

  // Delete a purchase order
  deletePurchaseOrder: async (id: string): Promise<void> => {
    await apiClient.delete(`/purchase-orders/${id}`);
  },



  // Cancel a purchase order
  cancelPurchaseOrder: async (id: string, data: { reason?: string } = {}): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.post(`/purchase-orders/${id}/cancel`, data);
    return response.data;
  },

  // Update purchase order status
  updatePurchaseOrderStatus: async (id: string, data: PurchaseOrderStatusUpdateDto): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.patch(`/purchase-orders/${id}/status`, data);
    return response.data;
  },

  // Get purchase order statistics
  getPurchaseOrderStats: async (period?: string): Promise<PurchaseOrderStatsResponse> => {
    const params = period ? `?period=${period}` : '';
    const response = await apiClient.get(`/purchase-orders/stats${params}`);
    return response.data;
  },

  // Export purchase orders
  exportPurchaseOrders: async (params: PurchaseOrderQueryParams & { format?: 'xlsx' | 'csv' } = {}): Promise<Blob> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/purchase-orders/export?${searchParams.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Generate purchase order PDF
  generatePurchaseOrderPdf: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/purchase-orders/${id}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Print purchase order
  printPurchaseOrder: async (id: string): Promise<Blob> => {
    const response = await apiClient.get(`/purchase-orders/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Send purchase order to supplier
  sendPurchaseOrder: async (id: string): Promise<PurchaseOrderWithRelations> => {
    const response = await apiClient.post(`/purchase-orders/${id}/send`);
    return response.data;
  },

  // Payment Management APIs
  // Get payment summary for a purchase order
  getPurchaseOrderPaymentSummary: async (id: string): Promise<PurchaseOrderPaymentSummary> => {
    const response = await apiClient.get(`/purchase-orders/${id}/payment-summary`);
    return response.data;
  },

  // Get payment schedule for a purchase order
  getPurchaseOrderPaymentSchedule: async (id: string): Promise<PaymentSchedule> => {
    const response = await apiClient.get(`/purchase-orders/${id}/payment-schedule`);
    return response.data;
  },

  // Get payments for a purchase order
  getPurchaseOrderPayments: async (id: string, params: PurchaseOrderPaymentQueryParams = {}): Promise<PaymentListResponse> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/purchase-orders/${id}/payments?${searchParams.toString()}`);
    return response.data;
  },

  // Create payment for a purchase order
  createPurchaseOrderPayment: async (id: string, data: CreatePurchaseOrderPaymentDto): Promise<SupplierPaymentWithRelations> => {
    const response = await apiClient.post(`/purchase-orders/${id}/payments`, data);
    return response.data;
  },

  // Update payment for a purchase order
  updatePurchaseOrderPayment: async (id: string, paymentId: string, data: Partial<CreatePurchaseOrderPaymentDto>): Promise<SupplierPaymentWithRelations> => {
    const response = await apiClient.patch(`/purchase-orders/${id}/payments/${paymentId}`, data);
    return response.data;
  },

  // Delete payment for a purchase order
  deletePurchaseOrderPayment: async (id: string, paymentId: string): Promise<void> => {
    await apiClient.delete(`/purchase-orders/${id}/payments/${paymentId}`);
  },

  // Get purchase order payment info (comprehensive data for payment management)
  getPurchaseOrderPaymentInfo: async (id: string): Promise<PurchaseOrderPaymentInfo> => {
    const response = await apiClient.get(`/purchase-orders/${id}/payment-info`);
    return response.data;
  },
};
