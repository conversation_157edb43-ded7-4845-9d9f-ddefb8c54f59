/**
 * Navigation progress types for global loading indicator
 */

export interface NavigationProgressState {
  /** Whether navigation is currently in progress */
  isLoading: boolean;
  /** Current progress percentage (0-100) */
  progress: number;
  /** Whether the loading bar should be visible */
  isVisible: boolean;
}

export interface NavigationProgressContextValue extends NavigationProgressState {
  /** Start loading manually */
  startLoading: () => void;
  /** Complete loading manually */
  completeLoading: () => void;
  /** Set specific progress value */
  setProgress: (progress: number) => void;
  /** Reset loading state */
  reset: () => void;
}

export interface NavigationProgressProviderProps {
  children: React.ReactNode;
  /** Custom configuration for the progress behavior */
  config?: NavigationProgressConfig;
}

export interface NavigationProgressConfig {
  /** Minimum time to show loading bar (ms) */
  minimumLoadingTime?: number;
  /** Animation duration for progress updates (ms) */
  animationDuration?: number;
  /** Delay before showing loading bar (ms) */
  showDelay?: number;
  /** Delay before hiding loading bar after completion (ms) */
  hideDelay?: number;
  /** Whether to show loading for same-page navigation */
  showForSamePage?: boolean;
  /** Maximum time to wait for compilation in development (ms) */
  maxCompilationTime?: number;
  /** Whether to enable router interception for immediate loading */
  enableRouterInterception?: boolean;
  /** Whether to detect and handle compilation delays */
  detectCompilation?: boolean;
}

export interface LoadingBarProps {
  /** Current progress percentage (0-100) */
  progress: number;
  /** Whether the loading bar is visible */
  isVisible: boolean;
  /** Custom className for styling */
  className?: string;
  /** Height of the loading bar in pixels */
  height?: number;
  /** Color theme for the loading bar */
  color?: 'primary' | 'secondary' | 'accent';
}

export type NavigationEvent = 'start' | 'complete' | 'error';

export interface NavigationEventHandler {
  (event: NavigationEvent, url?: string): void;
}

export interface RouterInterceptor {
  /** Original router push function */
  originalPush: (href: string, options?: any) => void;
  /** Original router replace function */
  originalReplace: (href: string, options?: any) => void;
  /** Whether interception is currently active */
  isActive: boolean;
  /** Start intercepting router calls */
  start: () => void;
  /** Stop intercepting router calls */
  stop: () => void;
}

export interface CompilationDetector {
  /** Whether compilation is currently detected */
  isCompiling: boolean;
  /** Start time of current compilation */
  compilationStartTime: number | null;
  /** Start detecting compilation */
  start: () => void;
  /** Stop detecting compilation */
  stop: () => void;
  /** Reset compilation state */
  reset: () => void;
}
