import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import {
  SupplierQueryParams,
  SupplierListResponse,
  SupplierStats,
  DocumentQueryParams,
  DocumentListResponse,
  PaymentQueryParams,
  PaymentListResponse,
  PaymentSummary,
  CreateSupplierDocumentDto,
  CreateSupplierPaymentDto,
  PaymentTermsCalculation,
  PaymentTermsSummary,
  PaymentTermsValidation,
  PaymentSchedule,
  PurchaseOrderForPayment,
  GoodsReceiptForPayment,
  type SupplierDocument,
} from '@/types/supplier';
import { suppliersApi } from '@/lib/api/suppliers';
import { toast } from 'sonner';

// Query key factory
export const supplierKeys = {
  all: ['suppliers'] as const,
  lists: () => [...supplierKeys.all, 'list'] as const,
  list: (params: SupplierQueryParams) => [...supplierKeys.lists(), params] as const,
  details: () => [...supplierKeys.all, 'detail'] as const,
  detail: (id: string) => [...supplierKeys.details(), id] as const,
  stats: () => [...supplierKeys.all, 'stats'] as const,
};

export function useSuppliers(params: SupplierQueryParams) {
  return useQuery({
    queryKey: supplierKeys.list(params),
    queryFn: () => suppliersApi.getSuppliers(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function useSupplier(id: string) {
  return useQuery({
    queryKey: supplierKeys.detail(id),
    queryFn: () => suppliersApi.getSupplier(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id && id !== 'none',
  });
}

export function useSupplierStats() {
  return useQuery({
    queryKey: supplierKeys.stats(),
    queryFn: () => suppliersApi.getStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useSuppliersPrefetch() {
  const queryClient = useQueryClient();
  
  const prefetchSuppliers = (params: SupplierQueryParams) => {
    return queryClient.prefetchQuery({
      queryKey: supplierKeys.list(params),
      queryFn: () => suppliersApi.getSuppliers(params),
      staleTime: 2 * 60 * 1000,
    });
  };

  return { prefetchSuppliers };
}

export function useSuppliersInvalidate() {
  const queryClient = useQueryClient();

  const invalidateSuppliers = () => {
    return queryClient.invalidateQueries({
      queryKey: supplierKeys.lists(),
    });
  };

  const invalidateStats = () => {
    return queryClient.invalidateQueries({
      queryKey: supplierKeys.stats(),
    });
  };

  const invalidateAllSuppliers = () => {
    return queryClient.invalidateQueries({
      queryKey: supplierKeys.all,
    });
  };

  return { invalidateSuppliers, invalidateStats, invalidateAllSuppliers };
}

// Document Management Hooks
export const documentKeys = {
  all: ['supplier-documents'] as const,
  lists: () => [...documentKeys.all, 'list'] as const,
  list: (supplierId: string, params: DocumentQueryParams) => [...documentKeys.lists(), supplierId, params] as const,
  details: () => [...documentKeys.all, 'detail'] as const,
  detail: (supplierId: string, documentId: string) => [...documentKeys.details(), supplierId, documentId] as const,
};

export function useSupplierDocuments(supplierId: string, params: DocumentQueryParams = {}) {
  return useQuery({
    queryKey: documentKeys.list(supplierId, params),
    queryFn: () => suppliersApi.getSupplierDocuments(supplierId, params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!supplierId,
  });
}

export function useSupplierDocument(supplierId: string, documentId: string) {
  return useQuery({
    queryKey: documentKeys.detail(supplierId, documentId),
    queryFn: () => suppliersApi.getSupplierDocument(supplierId, documentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId && !!documentId,
  });
}

// Payment Management Hooks
export const paymentKeys = {
  all: ['supplier-payments'] as const,
  lists: () => [...paymentKeys.all, 'list'] as const,
  list: (supplierId: string, params: PaymentQueryParams) => [...paymentKeys.lists(), supplierId, params] as const,
  details: () => [...paymentKeys.all, 'detail'] as const,
  detail: (supplierId: string, paymentId: string) => [...paymentKeys.details(), supplierId, paymentId] as const,
  summary: (supplierId: string) => [...paymentKeys.all, 'summary', supplierId] as const,
};

export function useSupplierPayments(supplierId: string, params: PaymentQueryParams = {}) {
  return useQuery({
    queryKey: paymentKeys.list(supplierId, params),
    queryFn: () => suppliersApi.getSupplierPayments(supplierId, params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!supplierId,
  });
}

export function useSupplierPayment(supplierId: string, paymentId: string) {
  return useQuery({
    queryKey: paymentKeys.detail(supplierId, paymentId),
    queryFn: () => suppliersApi.getSupplierPayment(supplierId, paymentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId && !!paymentId,
  });
}

export function useSupplierPaymentSummary(supplierId: string) {
  return useQuery({
    queryKey: paymentKeys.summary(supplierId),
    queryFn: () => suppliersApi.getSupplierPaymentSummary(supplierId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId,
  });
}

// Mutation Hooks

// Supplier CRUD Mutations
export function useDeleteSupplier() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (supplierId: string) => suppliersApi.deleteSupplier(supplierId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.stats() });
    },
  });
}

export function useActivateSupplier() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (supplierId: string) => suppliersApi.activateSupplier(supplierId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.stats() });
    },
  });
}

export function useDeactivateSupplier() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (supplierId: string) => suppliersApi.deactivateSupplier(supplierId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.stats() });
    },
  });
}

export function useHardDeleteSupplier() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (supplierId: string) => suppliersApi.hardDeleteSupplier(supplierId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.stats() });
    },
  });
}

// Document Management Mutations
export function useCreateSupplierDocument() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, data }: { supplierId: string; data: CreateSupplierDocumentDto }) =>
      suppliersApi.createSupplierDocument(supplierId, data),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: documentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

export function useUploadSupplierDocument() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      supplierId,
      file,
      metadata
    }: {
      supplierId: string;
      file: File;
      metadata: { type: string; name: string; description?: string }
    }) => suppliersApi.uploadSupplierDocument(supplierId, file, metadata),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: documentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

export function useDeleteSupplierDocument() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, documentId }: { supplierId: string; documentId: string }) =>
      suppliersApi.deleteSupplierDocument(supplierId, documentId),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: documentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

export function useCreateSupplierPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, data }: { supplierId: string; data: CreateSupplierPaymentDto }) =>
      suppliersApi.createSupplierPayment(supplierId, data),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.summary(supplierId) });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

export function useUpdateSupplierPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      supplierId,
      paymentId,
      data
    }: {
      supplierId: string;
      paymentId: string;
      data: Partial<CreateSupplierPaymentDto>
    }) => suppliersApi.updateSupplierPayment(supplierId, paymentId, data),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.summary(supplierId) });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

export function useDeleteSupplierPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, paymentId }: { supplierId: string; paymentId: string }) =>
      suppliersApi.deleteSupplierPayment(supplierId, paymentId),
    onSuccess: (_, { supplierId }) => {
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.summary(supplierId) });
      queryClient.invalidateQueries({ queryKey: supplierKeys.detail(supplierId) });
    },
  });
}

// Payment Proof Hooks
export const paymentProofKeys = {
  all: ['payment-proofs'] as const,
  detail: (supplierId: string, paymentId: string) => [...paymentProofKeys.all, 'detail', supplierId, paymentId] as const,
};

export function usePaymentProof(supplierId: string, paymentId: string) {
  return useQuery({
    queryKey: paymentProofKeys.detail(supplierId, paymentId),
    queryFn: () => suppliersApi.getPaymentProof(supplierId, paymentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId && !!paymentId,
  });
}

export function useUploadPaymentProof() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      supplierId,
      paymentId,
      file,
      data
    }: {
      supplierId: string;
      paymentId: string;
      file: File;
      data: { name: string; description?: string }
    }) =>
      suppliersApi.uploadPaymentProof(supplierId, paymentId, file, data),
    onSuccess: (_, { supplierId, paymentId }) => {
      queryClient.invalidateQueries({ queryKey: paymentProofKeys.detail(supplierId, paymentId) });
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.summary(supplierId) });
    },
  });
}

export function useDeletePaymentProof() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ supplierId, paymentId }: { supplierId: string; paymentId: string }) =>
      suppliersApi.deletePaymentProof(supplierId, paymentId),
    onSuccess: (_, { supplierId, paymentId }) => {
      queryClient.invalidateQueries({ queryKey: paymentProofKeys.detail(supplierId, paymentId) });
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: paymentKeys.summary(supplierId) });
    },
  });
}

// Payment reference number generation hooks
export function useGeneratePaymentReference() {
  return useMutation({
    mutationFn: () => suppliersApi.generatePaymentReference(),
  });
}

export function useValidatePaymentReference() {
  return useMutation({
    mutationFn: (reference: string) => suppliersApi.validatePaymentReference(reference),
  });
}

// Payment Terms Hooks
export const paymentTermsKeys = {
  all: ['payment-terms'] as const,
  summary: (supplierId: string) => [...paymentTermsKeys.all, 'summary', supplierId] as const,
  calculation: (supplierId: string, purchaseOrderId?: string, manualTerms?: number) =>
    [...paymentTermsKeys.all, 'calculation', supplierId, purchaseOrderId, manualTerms] as const,
  schedule: (purchaseOrderId: string) => [...paymentTermsKeys.all, 'schedule', purchaseOrderId] as const,
  validation: () => [...paymentTermsKeys.all, 'validation'] as const,
};

export function usePaymentTermsSummary(supplierId: string) {
  return useQuery({
    queryKey: paymentTermsKeys.summary(supplierId),
    queryFn: () => suppliersApi.getPaymentTermsSummary(supplierId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId,
  });
}

export function useCalculatePaymentTerms(
  supplierId: string,
  purchaseOrderId?: string,
  manualTerms?: number
) {
  return useQuery({
    queryKey: paymentTermsKeys.calculation(supplierId, purchaseOrderId, manualTerms),
    queryFn: () => suppliersApi.calculatePaymentTerms(supplierId, purchaseOrderId, manualTerms),
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!supplierId,
  });
}

export function useValidatePaymentTerms() {
  return useMutation({
    mutationFn: ({ supplierId, paymentData }: {
      supplierId: string;
      paymentData: CreateSupplierPaymentDto
    }) => suppliersApi.validatePaymentTerms(supplierId, paymentData),
  });
}

export function usePaymentSchedule(purchaseOrderId: string) {
  return useQuery({
    queryKey: paymentTermsKeys.schedule(purchaseOrderId),
    queryFn: () => suppliersApi.getPaymentSchedule(purchaseOrderId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!purchaseOrderId,
  });
}

// Purchase Order and Goods Receipt Integration Hooks
export const purchaseOrderKeys = {
  all: ['purchase-orders'] as const,
  forPayment: (supplierId: string) => [...purchaseOrderKeys.all, 'for-payment', supplierId] as const,
};

export const goodsReceiptKeys = {
  all: ['goods-receipts'] as const,
  forPayment: (supplierId: string, purchaseOrderId?: string) =>
    [...goodsReceiptKeys.all, 'for-payment', supplierId, purchaseOrderId] as const,
};

export function usePurchaseOrdersForPayment(supplierId: string) {
  return useQuery({
    queryKey: purchaseOrderKeys.forPayment(supplierId),
    queryFn: () => suppliersApi.getPurchaseOrdersForPayment(supplierId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId,
  });
}

export function useGoodsReceiptsForPayment(supplierId: string, purchaseOrderId?: string) {
  return useQuery({
    queryKey: goodsReceiptKeys.forPayment(supplierId, purchaseOrderId),
    queryFn: () => suppliersApi.getGoodsReceiptsForPayment(supplierId, purchaseOrderId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!supplierId,
  });
}

// Overdue Payment Management Hooks
export const overduePaymentKeys = {
  all: ['overdue-payments'] as const,
  list: () => [...overduePaymentKeys.all, 'list'] as const,
  compliance: () => [...overduePaymentKeys.all, 'compliance'] as const,
};

export function useOverduePayments() {
  return useQuery({
    queryKey: overduePaymentKeys.list(),
    queryFn: () => suppliersApi.getOverduePayments(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useUpdateOverduePaymentStatuses() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => suppliersApi.updateOverduePaymentStatuses(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: overduePaymentKeys.all });
      queryClient.invalidateQueries({ queryKey: paymentKeys.all });
      queryClient.invalidateQueries({ queryKey: paymentTermsKeys.all });
    },
  });
}

export function useGenerateOverdueReport() {
  return useMutation({
    mutationFn: () => suppliersApi.generateOverdueReport(),
  });
}

export function usePaymentComplianceReport() {
  return useQuery({
    queryKey: overduePaymentKeys.compliance(),
    queryFn: () => suppliersApi.getPaymentComplianceReport(),
    staleTime: 10 * 60 * 1000, // 10 minutes - compliance reports are expensive
  });
}

// Supplier Code Generation Hooks
export function useGenerateSupplierCode() {
  return useMutation({
    mutationFn: (type: string) => suppliersApi.generateSupplierCode(type),
  });
}

export function useValidateSupplierCode() {
  return useMutation({
    mutationFn: (code: string) => suppliersApi.validateSupplierCode(code),
  });
}

export async function handleDownload(supplierId: string, doc: SupplierDocument) {
    try {
      toast.loading('Mengunduh dokumen...', { id: `download-${doc.id}` });

      const blob = await suppliersApi.downloadSupplierDocument(supplierId, doc.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = doc.fileName || doc.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Dokumen berhasil diunduh', { id: `download-${doc.id}` });
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Gagal mengunduh dokumen', { id: `download-${doc.id}` });
    }
  };

export async function handleView(supplierId: string, doc: SupplierDocument) {
    try {
      toast.loading('Membuka dokumen...', { id: `view-${doc.id}` });

      const blob = await suppliersApi.downloadSupplierDocument(supplierId, doc.id);
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');

      toast.success('Dokumen berhasil dibuka', { id: `view-${doc.id}` });
    } catch (error) {
      console.error('View failed:', error);
      toast.error('Gagal membuka dokumen', { id: `view-${doc.id}` });
    }
  };