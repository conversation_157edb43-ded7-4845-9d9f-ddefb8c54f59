'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Eye, Edit, Trash2, CheckCircle, XCircle, Clock, AlertTriangle, FileText, HelpCircleIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { GoodsReceipt, GoodsReceiptStatus, GoodsReceiptWithRelations } from '@/types/goods-receipt';
import { formatCurrency, formatDate, formatDateTime } from '@/lib/utils';
import Link from 'next/link';
import { toast } from 'sonner';

interface GoodsReceiptActionsProps {
  goodsReceipt: GoodsReceiptWithRelations;
  onView: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onEdit: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onDelete: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onReject: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onComplete: (goodsReceipt: GoodsReceiptWithRelations) => void;
}

function GoodsReceiptActions({
  goodsReceipt,
  onView,
  onEdit,
  onDelete,
  onReject,
  onComplete,

}: GoodsReceiptActionsProps) {
  const canEdit = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canReject = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canComplete = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canDelete = goodsReceipt.status === GoodsReceiptStatus.PENDING;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Buka menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => onView(goodsReceipt)}>
          <Eye className="mr-2 h-4 w-4" />
          Lihat Cepat
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/dashboard/goods-receipts/${goodsReceipt.id}`}>
            <FileText className="mr-2 h-4 w-4" />
            Lihat Detail
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {canReject && (
          <DropdownMenuItem onClick={() => onReject(goodsReceipt)}>
            <XCircle className="mr-2 h-4 w-4" />
            Tolak
          </DropdownMenuItem>
        )}
        {canComplete && (
          <DropdownMenuItem onClick={() => onEdit(goodsReceipt)}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Terima Barang
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        {canDelete && (
          <DropdownMenuItem
            onClick={() => onDelete(goodsReceipt)}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Hapus
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function getStatusBadge(status: GoodsReceiptStatus) {
  const statusConfig = {
    [GoodsReceiptStatus.PENDING]: {
      label: 'Menunggu',
      variant: 'secondary' as const,
      icon: Clock,
    },
    [GoodsReceiptStatus.REJECTED]: {
      label: 'Ditolak',
      variant: 'destructive' as const,
      icon: XCircle,
    },
    [GoodsReceiptStatus.COMPLETED]: {
      label: 'Selesai',
      variant: 'default' as const,
      icon: CheckCircle,
    },
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}

export function createGoodsReceiptColumns(actions: {
  onView: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onEdit: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onDelete: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onReject: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onComplete: (goodsReceipt: GoodsReceiptWithRelations) => void;
  goToPage: (routerPath: string) => void;
}): ColumnDef<GoodsReceiptWithRelations>[] {
  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Pilih semua"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Pilih baris"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'receiptNumber',
      header: 'Nomor Penerimaan',
      cell: ({ row }) => {
        const receiptNumber = row.getValue('receiptNumber') as string;
        return (
          <div className="font-medium">
            {receiptNumber}
          </div>
        );
      },
    },
    {
      accessorKey: 'supplier',
      header: 'Supplier',
      cell: ({ row }) => {
        const goodsReceipt = row.original;
        // For list view, we only have supplierId, not the full supplier object
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex flex-col cursor-pointer" onClick={(e) => {
                e.stopPropagation();
                actions.goToPage(`/dashboard/suppliers/${goodsReceipt.supplierId}`);
              }}>
                <span className="font-medium">{goodsReceipt.supplier.name}</span>
                <div className="space-x-1">
                  <span className="text-xs text-muted-foreground">{goodsReceipt.supplier.code}</span>
                  <span className="text-xs text-muted-foreground">•</span>
                  <span className="text-xs text-muted-foreground">{goodsReceipt.supplier.type}</span>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              Klik untuk melihat detail supplier
            </TooltipContent>
          </Tooltip>
        );
      },
    },
    {
      accessorKey: 'purchaseOrder',
      header: 'Purchase Order',
      cell: ({ row }) => {
        const goodsReceipt = row.original;
        if (!goodsReceipt.purchaseOrderId) {
          return <span className="text-muted-foreground">-</span>;
        }
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex flex-col cursor-pointer" onClick={(e) => {
                e.stopPropagation()
                if(!goodsReceipt.purchaseOrder) return toast.error('Tidak memiliki purchase order');
                actions.goToPage(`/dashboard/purchase-orders/${goodsReceipt.purchaseOrderId}`);
              }}>
                <span className="font-medium">{goodsReceipt.purchaseOrder?.orderNumber}</span>
                <span className="text-xs text-muted-foreground">
                  {goodsReceipt.purchaseOrder && formatDate(goodsReceipt.purchaseOrder.orderDate)}
                </span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              Klik untuk melihat detail purchase order
            </TooltipContent>
          </Tooltip>
        );
      },
    },
    {
      accessorKey: 'receiptDate',
      header: () => (
        <div className="flex items-center gap-1">
          <span>Est. Tgl. Terima</span>
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircleIcon className="h-4 w-4" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Estimasi Tanggal penerimaan barang</p>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
      cell: ({ row }) => {
        const receiptDate = row.getValue('receiptDate') as string;
        return formatDate(receiptDate);
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as GoodsReceiptStatus;
        return getStatusBadge(status);
      },
    },

    {
      accessorKey: 'totalAmount',
      header: 'Total',
      cell: ({ row }) => {
        const totalAmount = row.getValue('totalAmount') as number;
        return (
          <div className="text-right font-medium">
            {formatCurrency(totalAmount)}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Dibuat',
      cell: ({ row }) => {
        const createdAt = row.getValue('createdAt') as string;
        const goodsReceipt = row.original;
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex flex-col cursor-help">
                  <span className="text-sm">{formatDate(createdAt)}</span>
                  {goodsReceipt.createdByUser && (
                    <span className="text-xs text-muted-foreground">
                      {goodsReceipt.createdByUser.firstName}
                    </span>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{formatDateTime(createdAt)}</p>
                {goodsReceipt.createdByUser && <p>Dibuat oleh: {goodsReceipt.createdByUser.firstName} {goodsReceipt.createdByUser.lastName}</p>}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      id: 'actions',
      enableHiding: false,
      header: () => (
        <div className="flex items-center justify-center gap-1">
          <span>Aksi</span>
          <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" title="Kolom ini selalu terlihat"></div>
        </div>
      ),
      cell: ({ row }) => {
        const goodsReceipt = row.original;
        return (
          <GoodsReceiptActions
            goodsReceipt={goodsReceipt}
            {...actions}
          />
        );
      },
    },
  ];
}
