'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Loader2, DollarSign, CreditCard, RefreshCw, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { CreateSupplierPaymentDto, PaymentMethod, PurchaseOrderForPayment, GoodsReceiptForPayment, PaymentTermsCalculation } from '@/types/supplier';
import { PAYMENT_METHOD_OPTIONS, PAYMENT_STATUS_OPTIONS } from '@/lib/constants/supplier';
import { formatCurrency } from '@/lib/utils';
import { PurchaseOrderSelector } from './PurchaseOrderSelector';
import { PaymentTermsValidator } from './PaymentTermsValidator';
import { useGeneratePaymentReference } from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { LiveCurrencyInput } from '@/components/ui/currency-input';

// Form validation schema
const supplierPaymentFormSchema = z.object({
  amount: z.number().min(0.01, 'Jumlah pembayaran harus lebih dari 0'),
  paymentMethod: z.nativeEnum(PaymentMethod, {
    required_error: 'Metode pembayaran harus dipilih',
  }),
  paymentDate: z.date({
    required_error: 'Tanggal pembayaran harus diisi',
  }),
  dueDate: z.date().optional(),
  status: z.string().min(1, 'Status pembayaran harus dipilih'),
  reference: z.string().optional(),
  notes: z.string().optional(),
  invoiceNumber: z.string().optional(),
  effectivePaymentTerms: z.number().optional(),
  purchaseOrderId: z.string().optional(),
  goodsReceiptId: z.string().optional(),
});

type SupplierPaymentFormData = z.infer<typeof supplierPaymentFormSchema>;

interface SupplierPaymentFormProps {
  supplierId: string;
  supplierName: string;
  initialData?: Partial<CreateSupplierPaymentDto>;
  onSubmit: (data: CreateSupplierPaymentDto) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
}

export function SupplierPaymentForm({
  supplierId,
  supplierName,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
}: SupplierPaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<PurchaseOrderForPayment | undefined>();
  const [selectedGoodsReceipt, setSelectedGoodsReceipt] = useState<GoodsReceiptForPayment | undefined>();
  const [calculatedPaymentTerms, setCalculatedPaymentTerms] = useState<PaymentTermsCalculation | undefined>();
  const [isPaymentTermsValid, setIsPaymentTermsValid] = useState(true);

  const generateReferenceMutation = useGeneratePaymentReference();

  const form = useForm<SupplierPaymentFormData>({
    resolver: zodResolver(supplierPaymentFormSchema),
    defaultValues: {
      amount: initialData?.amount || 0,
      paymentMethod: initialData?.paymentMethod || PaymentMethod.TRANSFER,
      paymentDate: initialData?.paymentDate ? new Date(initialData.paymentDate) : new Date(),
      dueDate: initialData?.dueDate ? new Date(initialData.dueDate) : undefined,
      status: initialData?.status || 'PENDING',
      reference: initialData?.reference || '',
      notes: initialData?.notes || '',
      invoiceNumber: initialData?.invoiceNumber || '',
      effectivePaymentTerms: initialData?.effectivePaymentTerms || undefined,
      purchaseOrderId: initialData?.purchaseOrderId || undefined,
      goodsReceiptId: initialData?.goodsReceiptId || undefined,
    },
  });

  // Auto-generate reference for new payments
  useEffect(() => {
    if (!isEditing && !initialData?.reference) {
      handleGenerateReference();
    }
  }, [isEditing, initialData?.reference]);

  const handleGenerateReference = async () => {
    try {
      const result = await generateReferenceMutation.mutateAsync();
      form.setValue('reference', result.reference);
      toast.success('Nomor referensi berhasil dibuat');
    } catch (error) {
      console.error('Failed to generate reference:', error);
      toast.error('Gagal membuat nomor referensi');
    }
  };

  const handleSubmit = async (data: SupplierPaymentFormData) => {
    if (!isPaymentTermsValid) {
      toast.error('Termin pembayaran tidak valid. Silakan periksa kembali.');
      return;
    }

    setIsSubmitting(true);
    try {
      const submitData: CreateSupplierPaymentDto = {
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        paymentDate: data.paymentDate.toISOString(),
        dueDate: data.dueDate?.toISOString(),
        status: data.status,
        reference: data.reference || undefined,
        notes: data.notes || undefined,
        invoiceNumber: data.invoiceNumber || undefined,
        effectivePaymentTerms: data.effectivePaymentTerms,
        purchaseOrderId: data.purchaseOrderId,
        goodsReceiptId: data.goodsReceiptId,
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Payment form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CreditCard className="h-5 w-5" />
          {isEditing ? 'Edit Pembayaran' : 'Tambah Pembayaran Baru'}
        </CardTitle>
        <CardDescription className="text-sm">
          {isEditing
            ? 'Perbarui informasi pembayaran untuk supplier ini'
            : `Masukkan detail pembayaran untuk supplier ${supplierName}`
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        {/* Purchase Order and Goods Receipt Selection */}
        <div className="mb-4">
          <PurchaseOrderSelector
            supplierId={supplierId}
            selectedPurchaseOrderId={selectedPurchaseOrder?.id}
            selectedGoodsReceiptId={selectedGoodsReceipt?.id}
            onPurchaseOrderChange={(id, po) => {
              setSelectedPurchaseOrder(po);
              form.setValue('purchaseOrderId', id);
            }}
            onGoodsReceiptChange={(id, gr) => {
              setSelectedGoodsReceipt(gr);
              form.setValue('goodsReceiptId', id);
            }}
            disabled={isSubmitting || isLoading}
          />
        </div>

        {/* Payment Terms Validation */}
        <div className="mb-4">
          <PaymentTermsValidator
            supplierId={supplierId}
            paymentData={{
              amount: form.watch('amount') || 0,
              paymentMethod: form.watch('paymentMethod'),
              paymentDate: form.watch('paymentDate')?.toISOString() || new Date().toISOString(),
              status: form.watch('status'),
              purchaseOrderId: selectedPurchaseOrder?.id,
              goodsReceiptId: selectedGoodsReceipt?.id,
            }}
            onValidationChange={(isValid, calculation) => {
              setIsPaymentTermsValid(isValid);
              setCalculatedPaymentTerms(calculation);
              if (calculation) {
                form.setValue('dueDate', calculation.calculatedDueDate ? new Date(calculation.calculatedDueDate) : undefined);
                form.setValue('effectivePaymentTerms', calculation.effectivePaymentTerms);
              }
            }}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Amount and Payment Method Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Jumlah Pembayaran *</FormLabel>
                    <FormControl>
                      <LiveCurrencyInput
                        id="amount"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Masukkan jumlah pembayaran"
                        required
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Metode Pembayaran *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih metode pembayaran" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_METHOD_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Payment Date and Due Date Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="paymentDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col space-y-1">
                    <FormLabel className="text-sm font-medium">Tanggal Pembayaran *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal h-10',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'dd/MM/yyyy', { locale: idLocale })
                            ) : (
                              <span>Pilih tanggal</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col space-y-1">
                    <FormLabel className="text-sm font-medium">Tanggal Jatuh Tempo</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal h-10',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'dd/MM/yyyy', { locale: idLocale })
                            ) : (
                              <span>Pilih tanggal (opsional)</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date('1900-01-01')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {calculatedPaymentTerms?.effectivePaymentTerms && (
                      <FormDescription className="text-xs">
                        Otomatis dihitung berdasarkan termin {calculatedPaymentTerms.effectivePaymentTerms} hari
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Status and Reference Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Status Pembayaran</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="Pilih status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_STATUS_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reference"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Nomor Referensi</FormLabel>
                    <FormControl>
                      <div className="flex gap-2">
                        <Input
                          placeholder="PAY-ASMSUP-100624-001"
                          className="flex-1 h-10"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleGenerateReference}
                          disabled={generateReferenceMutation.isPending}
                          className="px-3 h-10"
                        >
                          {generateReferenceMutation.isPending ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <RefreshCw className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs">
                      Format: PAY-{'{INISIAL}'}SUP-DDMMYY-{'{URUTAN}'} • Klik tombol untuk membuat otomatis
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Invoice Number and Notes Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Nomor Invoice</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor invoice dari supplier"
                        className="h-10"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-sm font-medium">Catatan</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Catatan tambahan untuk pembayaran ini..."
                        className="min-h-[60px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Validation Message */}
            {!isPaymentTermsValid && (
              <div className="flex items-center text-sm text-red-600 bg-red-50 p-2 rounded-md border border-red-200">
                <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>Termin pembayaran tidak valid</span>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-2 pt-2">
              <Button
                type="submit"
                disabled={isSubmitting || isLoading || !isPaymentTermsValid}
                className="flex-1 h-10"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Memperbarui...' : 'Menyimpan...'}
                  </>
                ) : (
                  <>
                    {!isPaymentTermsValid && <AlertTriangle className="mr-2 h-4 w-4" />}
                    <CreditCard className="mr-2 h-4 w-4" />
                    {isEditing ? 'Perbarui Pembayaran' : 'Simpan Pembayaran'}
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || isLoading}
                className="sm:w-auto h-10"
              >
                Batal
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
