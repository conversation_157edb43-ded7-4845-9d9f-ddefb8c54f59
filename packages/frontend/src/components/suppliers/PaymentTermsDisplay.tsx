'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  CreditCard,
  AlertCircle
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { PaymentTermsCalculation, PaymentTermsSummary, PaymentSchedule } from '@/types/supplier';

interface PaymentTermsDisplayProps {
  calculation?: PaymentTermsCalculation;
  summary?: PaymentTermsSummary;
  className?: string;
}

export function PaymentTermsDisplay({ calculation, summary, className }: PaymentTermsDisplayProps) {
  if (!calculation && !summary) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Informasi Termin Pembayaran
        </CardTitle>
        <CardDescription>
          Detail termin pembayaran dan jadwal jatuh tempo
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {calculation && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Termin Efektif</span>
              <div className="flex items-center gap-2">
                <span className="font-medium">
                  {calculation.effectivePaymentTerms === 0 
                    ? 'Pembayaran Langsung' 
                    : `${calculation.effectivePaymentTerms} hari`
                  }
                </span>
                <PaymentTermsSourceBadge source={calculation.source} />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Tanggal Jatuh Tempo</span>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">
                  {new Date(calculation.calculatedDueDate).toLocaleDateString('id-ID', {
                    weekday: 'short',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}
                </span>
              </div>
            </div>

            {calculation.isOverdue && (
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">Pembayaran Terlambat</span>
                </div>
                <Badge variant="destructive">
                  {calculation.daysOverdue} hari terlambat
                </Badge>
              </div>
            )}
          </div>
        )}

        {summary && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-medium">Ringkasan Pembayaran Supplier</h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <span className="text-xs text-muted-foreground">Total Outstanding</span>
                  <p className="font-medium text-lg">{formatCurrency(summary.totalOutstanding)}</p>
                </div>
                <div className="space-y-1">
                  <span className="text-xs text-muted-foreground">Total Terlambat</span>
                  <p className="font-medium text-lg text-red-600">{formatCurrency(summary.totalOverdue)}</p>
                </div>
                <div className="space-y-1">
                  <span className="text-xs text-muted-foreground">Rata-rata Hari Bayar</span>
                  <p className="font-medium">{summary.averagePaymentDays} hari</p>
                </div>
                <div className="space-y-1">
                  <span className="text-xs text-muted-foreground">Tingkat Kepatuhan</span>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{summary.complianceRate}%</p>
                    <ComplianceRateBadge rate={summary.complianceRate} />
                  </div>
                </div>
              </div>

              {summary.overdueCount > 0 && (
                <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    {summary.overdueCount} pembayaran terlambat memerlukan perhatian
                  </span>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

interface PaymentTermsSourceBadgeProps {
  source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL';
}

function PaymentTermsSourceBadge({ source }: PaymentTermsSourceBadgeProps) {
  const sourceConfig = {
    PURCHASE_ORDER: { 
      label: 'Purchase Order', 
      variant: 'default' as const,
      className: 'bg-blue-100 text-blue-800 hover:bg-blue-100'
    },
    SUPPLIER_DEFAULT: { 
      label: 'Default Supplier', 
      variant: 'secondary' as const,
      className: 'bg-gray-100 text-gray-800 hover:bg-gray-100'
    },
    MANUAL: { 
      label: 'Manual', 
      variant: 'outline' as const,
      className: 'bg-purple-100 text-purple-800 hover:bg-purple-100'
    },
  };

  const config = sourceConfig[source];

  return (
    <Badge variant={config.variant} className={`text-xs ${config.className}`}>
      {config.label}
    </Badge>
  );
}

interface ComplianceRateBadgeProps {
  rate: number;
}

function ComplianceRateBadge({ rate }: ComplianceRateBadgeProps) {
  if (rate >= 90) {
    return (
      <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
        <CheckCircle className="h-3 w-3 mr-1" />
        Sangat Baik
      </Badge>
    );
  } else if (rate >= 75) {
    return (
      <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
        <TrendingUp className="h-3 w-3 mr-1" />
        Baik
      </Badge>
    );
  } else if (rate >= 50) {
    return (
      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
        <Clock className="h-3 w-3 mr-1" />
        Perlu Perbaikan
      </Badge>
    );
  } else {
    return (
      <Badge variant="destructive" className="bg-red-100 text-red-800 hover:bg-red-100">
        <AlertTriangle className="h-3 w-3 mr-1" />
        Buruk
      </Badge>
    );
  }
}

interface PaymentScheduleListProps {
  schedules: PaymentSchedule[];
  className?: string;
}

export function PaymentScheduleList({ schedules, className }: PaymentScheduleListProps) {
  if (!schedules || schedules.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center text-muted-foreground">
          <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Tidak ada jadwal pembayaran</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Jadwal Pembayaran
        </CardTitle>
        <CardDescription>
          {schedules.length} pembayaran yang perlu diproses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {schedules.map((schedule) => (
            <PaymentScheduleItem key={schedule.purchaseOrderId} schedule={schedule} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface PaymentScheduleItemProps {
  schedule: PaymentSchedule;
}

function PaymentScheduleItem({ schedule }: PaymentScheduleItemProps) {
  const isOverdue = schedule.isOverdue;
  const isDueSoon = !isOverdue && schedule.daysOverdue >= -3; // Due within 3 days

  return (
    <div className={`p-3 rounded-lg border ${
      isOverdue 
        ? 'border-red-200 bg-red-50' 
        : isDueSoon 
          ? 'border-yellow-200 bg-yellow-50'
          : 'border-gray-200 bg-gray-50'
    }`}>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-medium">{schedule.orderNumber}</span>
            {isOverdue && (
              <Badge variant="destructive" className="text-xs">
                {schedule.daysOverdue} hari terlambat
              </Badge>
            )}
            {isDueSoon && !isOverdue && (
              <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-800">
                Jatuh tempo segera
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            Jatuh tempo: {new Date(schedule.calculatedDueDate).toLocaleDateString('id-ID')}
          </p>
          <p className="text-sm text-muted-foreground">
            Termin: {schedule.effectivePaymentTerms} hari
          </p>
        </div>
        <div className="text-right space-y-1">
          <p className="font-medium">{formatCurrency(schedule.remainingAmount)}</p>
          <p className="text-xs text-muted-foreground">
            dari {formatCurrency(schedule.totalAmount)}
          </p>
          <Badge variant={schedule.paymentStatus === 'OVERDUE' ? 'destructive' : 'secondary'}>
            {schedule.paymentStatus === 'PENDING' ? 'Menunggu' :
             schedule.paymentStatus === 'PARTIAL' ? 'Sebagian' :
             schedule.paymentStatus === 'OVERDUE' ? 'Terlambat' : schedule.paymentStatus}
          </Badge>
        </div>
      </div>
    </div>
  );
}
