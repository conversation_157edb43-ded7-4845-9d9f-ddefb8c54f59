'use client';

import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, Package, Calendar, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import { PurchaseOrderForPayment, GoodsReceiptForPayment } from '@/types/supplier';
import { usePurchaseOrdersForPayment, useGoodsReceiptsForPayment } from '@/hooks/useSuppliers';

interface PurchaseOrderSelectorProps {
  supplierId: string;
  selectedPurchaseOrderId?: string;
  selectedGoodsReceiptId?: string;
  onPurchaseOrderChange: (purchaseOrderId: string | undefined, purchaseOrder: PurchaseOrderForPayment | undefined) => void;
  onGoodsReceiptChange: (goodsReceiptId: string | undefined, goodsReceipt: GoodsReceiptForPayment | undefined) => void;
  disabled?: boolean;
  className?: string;
}

export function PurchaseOrderSelector({
  supplierId,
  selectedPurchaseOrderId,
  selectedGoodsReceiptId,
  onPurchaseOrderChange,
  onGoodsReceiptChange,
  disabled = false,
  className,
}: PurchaseOrderSelectorProps) {
  const [openPO, setOpenPO] = useState(false);
  const [openGR, setOpenGR] = useState(false);

  const { data: purchaseOrders, isLoading: isLoadingPO } = usePurchaseOrdersForPayment(supplierId);
  const { data: goodsReceipts, isLoading: isLoadingGR } = useGoodsReceiptsForPayment(
    supplierId, 
    selectedPurchaseOrderId
  );

  const selectedPO = purchaseOrders?.find(po => po.id === selectedPurchaseOrderId);
  const selectedGR = goodsReceipts?.find(gr => gr.id === selectedGoodsReceiptId);

  // Reset goods receipt when purchase order changes
  useEffect(() => {
    if (selectedPurchaseOrderId && selectedGoodsReceiptId) {
      const isGRValid = goodsReceipts?.some(gr => 
        gr.id === selectedGoodsReceiptId && 
        gr.purchaseOrder.id === selectedPurchaseOrderId
      );
      
      if (!isGRValid) {
        onGoodsReceiptChange(undefined, undefined);
      }
    }
  }, [selectedPurchaseOrderId, goodsReceipts, selectedGoodsReceiptId, onGoodsReceiptChange]);

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: 'secondary' as const, label: 'Menunggu' },
      PARTIAL: { variant: 'outline' as const, label: 'Sebagian' },
      OVERDUE: { variant: 'destructive' as const, label: 'Terlambat' },
      PAID: { variant: 'default' as const, label: 'Lunas' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Purchase Order Selector */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Purchase Order (Opsional)</label>
        <Popover open={openPO} onOpenChange={setOpenPO}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={openPO}
              className="w-full justify-between"
              disabled={disabled || isLoadingPO}
            >
              {selectedPO ? (
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <span className="truncate">{selectedPO.orderNumber}</span>
                  <span className="text-muted-foreground">•</span>
                  <span className="text-sm text-muted-foreground">
                    {formatCurrency(selectedPO.totalAmount)}
                  </span>
                </div>
              ) : (
                <span className="text-muted-foreground">Pilih Purchase Order...</span>
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandInput placeholder="Cari purchase order..." />
              <CommandEmpty>
                {isLoadingPO ? 'Memuat...' : 'Tidak ada purchase order ditemukan.'}
              </CommandEmpty>
              <CommandGroup className="max-h-64 overflow-auto">
                <CommandItem
                  onSelect={() => {
                    onPurchaseOrderChange(undefined, undefined);
                    setOpenPO(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      !selectedPurchaseOrderId ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="text-muted-foreground">Tidak ada purchase order</span>
                </CommandItem>
                {purchaseOrders?.map((po) => (
                  <CommandItem
                    key={po.id}
                    onSelect={() => {
                      onPurchaseOrderChange(po.id, po);
                      setOpenPO(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedPurchaseOrderId === po.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{po.orderNumber}</span>
                        {getPaymentStatusBadge(po.paymentStatus)}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(po.orderDate).toLocaleDateString('id-ID')}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          {formatCurrency(po.totalAmount)}
                        </div>
                        {po.paymentTerms && (
                          <span>Termin: {po.paymentTerms} hari</span>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        <p className="text-xs text-muted-foreground">
          Pilih purchase order untuk menggunakan termin pembayaran yang sudah ditetapkan
        </p>
      </div>

      {/* Goods Receipt Selector - Only show if PO is selected */}
      {selectedPurchaseOrderId && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Goods Receipt (Opsional)</label>
          <Popover open={openGR} onOpenChange={setOpenGR}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={openGR}
                className="w-full justify-between"
                disabled={disabled || isLoadingGR}
              >
                {selectedGR ? (
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{selectedGR.receiptNumber}</span>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-sm text-muted-foreground">
                      {formatCurrency(selectedGR.totalAmount)}
                    </span>
                  </div>
                ) : (
                  <span className="text-muted-foreground">Pilih Goods Receipt...</span>
                )}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Command>
                <CommandInput placeholder="Cari goods receipt..." />
                <CommandEmpty>
                  {isLoadingGR ? 'Memuat...' : 'Tidak ada goods receipt ditemukan.'}
                </CommandEmpty>
                <CommandGroup className="max-h-64 overflow-auto">
                  <CommandItem
                    onSelect={() => {
                      onGoodsReceiptChange(undefined, undefined);
                      setOpenGR(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        !selectedGoodsReceiptId ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <span className="text-muted-foreground">Tidak ada goods receipt</span>
                  </CommandItem>
                  {goodsReceipts?.map((gr) => (
                    <CommandItem
                      key={gr.id}
                      onSelect={() => {
                        onGoodsReceiptChange(gr.id, gr);
                        setOpenGR(false);
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedGoodsReceiptId === gr.id ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{gr.receiptNumber}</span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(gr.receiptDate).toLocaleDateString('id-ID')}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {formatCurrency(gr.totalAmount)}
                          </div>
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
          <p className="text-xs text-muted-foreground">
            Pilih goods receipt yang terkait dengan purchase order ini
          </p>
        </div>
      )}

      {/* Selected Purchase Order Info */}
      {selectedPO && (
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <Package className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-900">Purchase Order Terpilih</span>
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-blue-700">Nomor:</span>
              <span className="font-medium text-blue-900">{selectedPO.orderNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">Tanggal:</span>
              <span className="font-medium text-blue-900">
                {new Date(selectedPO.orderDate).toLocaleDateString('id-ID')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">Total:</span>
              <span className="font-medium text-blue-900">{formatCurrency(selectedPO.totalAmount)}</span>
            </div>
            {selectedPO.paymentTerms && (
              <div className="flex justify-between">
                <span className="text-blue-700">Termin Pembayaran:</span>
                <span className="font-medium text-blue-900">{selectedPO.paymentTerms} hari</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-blue-700">Status Pembayaran:</span>
              <div>{getPaymentStatusBadge(selectedPO.paymentStatus)}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
