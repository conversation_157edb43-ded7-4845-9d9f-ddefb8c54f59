'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { PurchaseOrderDetail } from '@/components/purchase-orders/purchase-order-detail';
import { SendToSupplierDialog } from '@/components/purchase-orders/send-to-supplier-dialog';
import { PurchaseOrderPaymentModal } from '@/components/purchase-orders/PurchaseOrderPaymentModal';
import {
  usePurchaseOrder,
  useCancelPurchaseOrder,
  useSendPurchaseOrder,
  useDownloadPurchaseOrderPdf,
} from '@/hooks/usePurchaseOrders';
import { navigateBackWithFallback } from '@/lib/utils/navigation';
import { toast } from 'sonner';

interface PurchaseOrderDetailPageClientProps {
  purchaseOrderId: string;
}

export function PurchaseOrderDetailPageClient({ purchaseOrderId }: PurchaseOrderDetailPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showSendToSupplierDialog, setShowSendToSupplierDialog] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentModalTab, setPaymentModalTab] = useState<'summary' | 'payments' | 'schedule' | 'add'>('summary');
  const [paymentModalAction, setPaymentModalAction] = useState<'add' | 'view' | 'edit' | undefined>();

  const { data: purchaseOrder, isLoading, error, refetch } = usePurchaseOrder(purchaseOrderId);
  const cancelPurchaseOrderMutation = useCancelPurchaseOrder();
  const sendPurchaseOrderMutation = useSendPurchaseOrder();
  const downloadPurchaseOrderPdfMutation = useDownloadPurchaseOrderPdf();

  // Handle URL parameters to automatically open payment modal
  useEffect(() => {
    const tab = searchParams.get('tab');
    const action = searchParams.get('action');

    if (tab && ['summary', 'payments', 'schedule', 'add', 'payment-summary'].includes(tab)) {
      // Map payment-summary to summary for consistency
      const modalTab = tab === 'payment-summary' ? 'summary' : tab as 'summary' | 'payments' | 'schedule' | 'add';

      setPaymentModalTab(modalTab);
      setPaymentModalAction(action as 'add' | 'view' | 'edit' | undefined);
      setShowPaymentModal(true);

      // Clean up URL parameters after opening modal
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('tab');
      newUrl.searchParams.delete('action');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);

  const handleEdit = () => {
    router.push(`/dashboard/purchase-orders/${purchaseOrderId}/edit`);
  };



  const handleCancel = async (reason: string) => {
    try {
      await cancelPurchaseOrderMutation.mutateAsync({
        id: purchaseOrderId,
        reason,
      });
      // Refresh the purchase order data
      refetch();
    } catch (error) {
      console.error('Failed to cancel purchase order:', error);
    }
  };

  const handleSend = () => {
    if (purchaseOrder) {
      setShowSendToSupplierDialog(true);
    }
  };

  const handlePrint = async () => {
    try {
      await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrderId);
    } catch (error) {
      console.error('Failed to print purchase order:', error);
    }
  };

  const handleDownloadPdf = async () => {
    await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrderId);
  };

  const handleActualSendToSupplier = async () => {
    try {
      await sendPurchaseOrderMutation.mutateAsync(purchaseOrderId);
      refetch();
    } catch (error) {
      console.error('Failed to send purchase order:', error);
    }
  };

  const handleBack = () => {
    navigateBackWithFallback(router, '/dashboard/purchase-orders');
  };

  // Payment handlers
  const handleViewPayments = () => {
    setPaymentModalTab('payments');
    setPaymentModalAction('view');
    setShowPaymentModal(true);
  };

  const handleAddPayment = () => {
    setPaymentModalTab('add');
    setPaymentModalAction('add');
    setShowPaymentModal(true);
  };

  const handleViewPaymentSummary = () => {
    setPaymentModalTab('summary');
    setPaymentModalAction('view');
    setShowPaymentModal(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton - Simplified for client loading */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="h-9 w-20" />
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-20" />
          </div>
        </div>

        {/* Content Skeleton - Focused on data-dependent parts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Info Card Skeleton */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Items Table Skeleton */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Sidebar Cards Skeleton */}
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    {Array.from({ length: 4 }).map((_, j) => (
                      <div key={j} className="space-y-1">
                        <Skeleton className="h-3 w-20" />
                        <Skeleton className="h-4 w-28" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
          >
            ← Kembali
          </button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Purchase Order Detail</h1>
            <p className="text-muted-foreground">
              Purchase order tidak ditemukan atau terjadi kesalahan
            </p>
          </div>
        </div>

        {/* Error Message */}
        <div className="rounded-lg border border-destructive/50 bg-destructive/10 p-4">
          <p className="text-sm text-destructive">
            {error ? 'Gagal memuat data purchase order' : 'Purchase order tidak ditemukan'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <PurchaseOrderDetail
        purchaseOrder={purchaseOrder}
        onEdit={handleEdit}
        onCancel={handleCancel}
        onSend={handleSend}
        onPrint={handlePrint}
        onBack={handleBack}
        onViewPayments={handleViewPayments}
        onAddPayment={handleAddPayment}
        onViewPaymentSummary={handleViewPaymentSummary}
        isLoading={
          cancelPurchaseOrderMutation.isPending ||
          sendPurchaseOrderMutation.isPending ||
          downloadPurchaseOrderPdfMutation.isPending
        }
      />

      {/* Send to Supplier Dialog */}
      <SendToSupplierDialog
        open={showSendToSupplierDialog}
        onOpenChange={setShowSendToSupplierDialog}
        purchaseOrder={purchaseOrder}
        onDownloadPdf={handleDownloadPdf}
        onSendToSupplier={handleActualSendToSupplier}
        isDownloading={downloadPurchaseOrderPdfMutation.isPending}
        isSending={sendPurchaseOrderMutation.isPending}
      />

      {/* Payment Management Modal */}
      <PurchaseOrderPaymentModal
        open={showPaymentModal}
        onOpenChange={(open) => {
          setShowPaymentModal(open);
          if (!open) {
            setPaymentModalTab('summary');
            setPaymentModalAction(undefined);
          }
        }}
        purchaseOrder={purchaseOrder}
        initialTab={paymentModalTab}
        initialAction={paymentModalAction}
      />
    </>
  );
}
