'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CreditCard,
  DollarSign,
  Calendar,
  FileText,
  Eye,
  Edit,
  Trash2,
  Plus,
} from 'lucide-react';
import { PurchaseOrderPaymentInfo } from '@/types/purchase-order';
import { PaymentStatusBadge } from './PaymentStatusBadge';
import { formatCurrency } from '@/lib/utils';
import { PAYMENT_METHOD_OPTIONS } from '@/lib/constants/supplier';

interface PaymentInfoDisplayProps {
  paymentInfo: PurchaseOrderPaymentInfo;
  onViewPayment?: (paymentId: string) => void;
  onEditPayment?: (paymentId: string) => void;
  onDeletePayment?: (paymentId: string) => void;
  onAddPayment?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function PaymentInfoDisplay({
  paymentInfo,
  onViewPayment,
  onEditPayment,
  onDeletePayment,
  onAddPayment,
  isLoading = false,
  className,
}: PaymentInfoDisplayProps) {
  const {
    orderNumber,
    supplierName,
    totalAmount,
    paymentStatus,
    paymentTerms,
    orderDate,
    dueDate,
    paymentSummary,
    payments,
  } = paymentInfo;

  const getPaymentMethodLabel = (method: string) => {
    return PAYMENT_METHOD_OPTIONS.find(opt => opt.value === method)?.label || method;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="space-y-2">
            <div className="h-6 w-48 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  <div className="h-5 w-32 bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Purchase Order Info */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi Purchase Order
              </CardTitle>
              <CardDescription>
                {orderNumber} - {supplierName}
              </CardDescription>
            </div>
            <PaymentStatusBadge status={paymentStatus} />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Total Amount</p>
              <p className="font-semibold">{formatCurrency(totalAmount)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Tanggal Order</p>
              <p className="font-medium">
                {new Date(orderDate).toLocaleDateString('id-ID')}
              </p>
            </div>
            {paymentTerms && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Termin Pembayaran</p>
                <p className="font-medium">{paymentTerms} hari</p>
              </div>
            )}
            {dueDate && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Jatuh Tempo</p>
                <p className="font-medium">
                  {new Date(dueDate).toLocaleDateString('id-ID')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Payment Summary */}
      {paymentSummary && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Ringkasan Pembayaran
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="font-semibold">{formatCurrency(paymentSummary.totalAmount || 0)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Dibayar</p>
                <p className="font-semibold text-green-600">
                  {formatCurrency(paymentSummary.paidAmount || 0)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Sisa</p>
                <p className="font-semibold text-blue-600">
                  {formatCurrency(paymentSummary.remainingAmount || 0)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Jumlah Pembayaran</p>
                <p className="font-semibold">{paymentSummary.paymentCount || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Riwayat Pembayaran
              </CardTitle>
              <CardDescription>
                Daftar semua pembayaran untuk purchase order ini
              </CardDescription>
            </div>
            {onAddPayment && (
              <Button onClick={onAddPayment} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Pembayaran
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {!payments || payments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Belum ada pembayaran</p>
              <p className="text-sm">Tambahkan pembayaran pertama untuk purchase order ini</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Jumlah</TableHead>
                  <TableHead>Metode</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Referensi</TableHead>
                  <TableHead className="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      {new Date(payment.paymentDate).toLocaleDateString('id-ID')}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>
                      {getPaymentMethodLabel(payment.paymentMethod)}
                    </TableCell>
                    <TableCell>
                      <PaymentStatusBadge status={payment.status} />
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {payment.reference || '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-1">
                        {onViewPayment && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewPayment(payment.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        {onEditPayment && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditPayment(payment.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                        {onDeletePayment && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeletePayment(payment.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
