'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Eye, Edit, Trash2, FileText, Send, XCircle, CreditCard, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { PurchaseOrderWithRelations, PurchaseOrderStatus, PaymentStatus } from '@/types/purchase-order';
import { getPurchaseOrderStatusLabel, getPurchaseOrderStatusColor } from '@/lib/constants/purchase-order';
import { formatCurrency, formatDate } from '@/lib/utils';
import { PaymentStatusBadge } from './PaymentStatusBadge';
import { PaymentActionButtons } from './PaymentActionButtons';
import Link from 'next/link';

interface PurchaseOrderActionsProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onView: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onEdit: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onDelete: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onCancel: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onSend: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onPrint: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onViewPayments?: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onAddPayment?: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onViewPaymentSummary?: (purchaseOrder: PurchaseOrderWithRelations) => void;
}

function PurchaseOrderActions({
  purchaseOrder,
  onView,
  onEdit,
  onDelete,
  onCancel,
  onSend,
  onPrint,
  onViewPayments,
  onAddPayment,
  onViewPaymentSummary,
}: PurchaseOrderActionsProps) {
  const canEdit = purchaseOrder.status === PurchaseOrderStatus.DRAFT;
  const canSend = purchaseOrder.status === PurchaseOrderStatus.SUBMITTED;
  const cancellableStatuses: PurchaseOrderStatus[] = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.ORDERED,
    PurchaseOrderStatus.PARTIALLY_RECEIVED,
  ];
  const canCancel = (cancellableStatuses as readonly PurchaseOrderStatus[]).includes(purchaseOrder.status);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={(e) => e.stopPropagation()}
        >
          <span className="sr-only">Buka menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={(e) => {
          e.stopPropagation();
          onView(purchaseOrder);
        }}>
          <Eye className="mr-2 h-4 w-4" />
          Lihat Cepat
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={`/dashboard/purchase-orders/${purchaseOrder.id}`} onClick={(e) => e.stopPropagation()}>
            <FileText className="mr-2 h-4 w-4" />
            Lihat Detail
          </Link>
        </DropdownMenuItem>

        {canEdit && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onEdit(purchaseOrder);
          }}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        )}

        <DropdownMenuItem onClick={(e) => {
          e.stopPropagation();
          onPrint(purchaseOrder);
        }}>
          <FileText className="mr-2 h-4 w-4" />
          Cetak PDF
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Payment Management */}
        {onViewPaymentSummary && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onViewPaymentSummary(purchaseOrder);
          }}>
            <DollarSign className="mr-2 h-4 w-4" />
            Ringkasan Pembayaran
          </DropdownMenuItem>
        )}

        {onViewPayments && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onViewPayments(purchaseOrder);
          }}>
            <CreditCard className="mr-2 h-4 w-4" />
            Kelola Pembayaran
          </DropdownMenuItem>
        )}

        {onAddPayment && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onAddPayment(purchaseOrder);
          }}>
            <CreditCard className="mr-2 h-4 w-4" />
            Tambah Pembayaran
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator />



        {canSend && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onSend(purchaseOrder);
          }}>
            <Send className="mr-2 h-4 w-4" />
            Kirim ke Supplier
          </DropdownMenuItem>
        )}

        {canCancel && (
          <DropdownMenuItem onClick={(e) => {
            e.stopPropagation();
            onCancel(purchaseOrder);
          }}>
            <XCircle className="mr-2 h-4 w-4" />
            Batalkan
          </DropdownMenuItem>
        )}

        {purchaseOrder.status === PurchaseOrderStatus.DRAFT && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onDelete(purchaseOrder);
              }}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Hapus
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function createPurchaseOrderColumns({
  onView,
  onEdit,
  onDelete,
  onCancel,
  onSend,
  onPrint,
  onViewPayments,
  onAddPayment,
  onViewPaymentSummary,
  goToPage,
}: {
  onView: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onEdit: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onDelete: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onCancel: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onSend: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onPrint: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onViewPayments?: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onAddPayment?: (purchaseOrder: PurchaseOrderWithRelations) => void;
  onViewPaymentSummary?: (purchaseOrder: PurchaseOrderWithRelations) => void;
  goToPage: (path: string) => void;
}): ColumnDef<PurchaseOrderWithRelations>[] {
  return [
    {
      accessorKey: 'orderNumber',
      header: 'Nomor PO',
      cell: ({ row }) => {
        const purchaseOrder = row.original;
        return (
          <div className="font-medium">
            {purchaseOrder.orderNumber}
          </div>
        );
      },
    },
    {
      accessorKey: 'supplier',
      header: 'Supplier',
      cell: ({ row }) => {
        const supplier = row.original.supplier;
        return (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex flex-col cursor-pointer" onClick={(e) => {
                e.stopPropagation();
                goToPage(`/dashboard/suppliers/${supplier.id}`);
              }}>
                <span className="font-medium">{supplier.name}</span>
                <div className="text-sm text-muted-foreground truncate">
                  {supplier.code} • {supplier.type}
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              Klik untuk melihat detail supplier
            </TooltipContent>
          </Tooltip>
        );
      },
    },
    {
      accessorKey: 'orderDate',
      header: 'Tanggal Order',
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {formatDate(row.original.orderDate)}
          </div>
        );
      },
    },
    {
      accessorKey: 'expectedDelivery',
      header: 'Estimasi Pengiriman',
      cell: ({ row }) => {
        const expectedDelivery = row.original.expectedDelivery;
        if (!expectedDelivery) {
          return <span className="text-muted-foreground">-</span>;
        }
        return (
          <div className="text-sm">
            {formatDate(expectedDelivery)}
          </div>
        );
      },
    },
    {
      accessorKey: 'totalAmount',
      header: 'Total',
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {formatCurrency(row.original.totalAmount)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        const statusLabel = getPurchaseOrderStatusLabel(status);
        const statusColor = getPurchaseOrderStatusColor(status);

        return (
          <Badge variant="outline" className={statusColor}>
            {statusLabel}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'paymentStatus',
      header: 'Status Pembayaran',
      cell: ({ row }) => {
        const purchaseOrder = row.original;
        const paymentStatus = (purchaseOrder.paymentStatus as PaymentStatus) || 'PENDING';

        return (
          <div className="flex items-center gap-2">
            <PaymentStatusBadge status={paymentStatus} />
            {purchaseOrder.paymentSummary && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs text-muted-foreground cursor-help">
                      {formatCurrency(purchaseOrder.paymentSummary.paidAmount)} / {formatCurrency(purchaseOrder.paymentSummary.totalAmount)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm">
                      <div>Dibayar: {formatCurrency(purchaseOrder.paymentSummary.paidAmount)}</div>
                      <div>Sisa: {formatCurrency(purchaseOrder.paymentSummary.remainingAmount)}</div>
                      <div>Jumlah Pembayaran: {purchaseOrder.paymentSummary.paymentCount}</div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'items',
      header: 'Item',
      cell: ({ row }) => {
        const items = row.original.items;
        const itemCount = items.length;
        const totalQuantity = items.reduce((sum, item) => sum + item.quantityOrdered, 0);

        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-sm cursor-help">
                  {itemCount} item{itemCount > 1 ? 's' : ''}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm">
                  <div>{itemCount} jenis produk</div>
                  <div>{totalQuantity} total kuantitas</div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Dibuat',
      cell: ({ row }) => {
        const createdBy = row.original.createdByUser;
        return (
          <div className="text-sm">
            <div>{formatDate(row.original.createdAt)}</div>
            {createdBy && (
              <div className="text-muted-foreground truncate">
                oleh {createdBy.firstName} {createdBy.lastName}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => {
        const purchaseOrder = row.original;

        return (
          <PurchaseOrderActions
            purchaseOrder={purchaseOrder}
            onView={onView}
            onEdit={onEdit}
            onDelete={onDelete}
            onCancel={onCancel}
            onSend={onSend}
            onPrint={onPrint}
            onViewPayments={onViewPayments}
            onAddPayment={onAddPayment}
            onViewPaymentSummary={onViewPaymentSummary}
          />
        );
      },
    },
  ];
}
