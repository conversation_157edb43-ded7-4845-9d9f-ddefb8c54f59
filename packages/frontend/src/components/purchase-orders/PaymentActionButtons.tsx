'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CreditCard,
  DollarSign,
  Eye,
  Plus,
  FileText,
  Calendar,
  MoreHorizontal,
  AlertTriangle,
} from 'lucide-react';
import { PurchaseOrderWithRelations, PaymentStatus } from '@/types/purchase-order';
import { PaymentStatusBadge } from './PaymentStatusBadge';

interface PaymentActionButtonsProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  onViewPaymentSummary?: () => void;
  onViewPaymentSchedule?: () => void;
  onManagePaymentTerms?: () => void;
  compact?: boolean;
  showDropdown?: boolean;
}

export function PaymentActionButtons({
  purchaseOrder,
  onViewPayments,
  onAddPayment,
  onViewPaymentSummary,
  onViewPaymentSchedule,
  onManagePaymentTerms,
  compact = false,
  showDropdown = true,
}: PaymentActionButtonsProps) {
  const paymentStatus = purchaseOrder.paymentStatus as PaymentStatus;
  const hasPayments = purchaseOrder.payments && purchaseOrder.payments.length > 0;
  const isOverdue = paymentStatus === 'OVERDUE';

  // Compact view - just status badge and dropdown
  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <PaymentStatusBadge status={paymentStatus} />
        {showDropdown && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Buka menu pembayaran</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onViewPaymentSummary && (
                <DropdownMenuItem onClick={onViewPaymentSummary}>
                  <Eye className="h-4 w-4 mr-2" />
                  Lihat Ringkasan
                </DropdownMenuItem>
              )}
              {onViewPayments && hasPayments && (
                <DropdownMenuItem onClick={onViewPayments}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Lihat Pembayaran
                </DropdownMenuItem>
              )}
              {onAddPayment && (
                <DropdownMenuItem onClick={onAddPayment}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pembayaran
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onViewPaymentSchedule && (
                <DropdownMenuItem onClick={onViewPaymentSchedule}>
                  <Calendar className="h-4 w-4 mr-2" />
                  Jadwal Pembayaran
                </DropdownMenuItem>
              )}
              {onManagePaymentTerms && (
                <DropdownMenuItem onClick={onManagePaymentTerms}>
                  <FileText className="h-4 w-4 mr-2" />
                  Kelola Termin
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  }

  // Full view - individual buttons
  return (
    <div className="flex items-center gap-2 flex-wrap">
      <PaymentStatusBadge status={paymentStatus} />
      
      {isOverdue && (
        <div className="flex items-center gap-1 text-red-600 text-sm">
          <AlertTriangle className="h-3 w-3" />
          <span className="text-xs">Terlambat</span>
        </div>
      )}

      <div className="flex gap-1">
        {onViewPaymentSummary && (
          <Button
            variant="outline"
            size="sm"
            onClick={onViewPaymentSummary}
            className="h-8"
          >
            <Eye className="h-3 w-3 mr-1" />
            Ringkasan
          </Button>
        )}

        {onAddPayment && (
          <Button
            size="sm"
            onClick={onAddPayment}
            className="h-8"
          >
            <Plus className="h-3 w-3 mr-1" />
            Bayar
          </Button>
        )}

        {showDropdown && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-3 w-3" />
                <span className="sr-only">Opsi pembayaran lainnya</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onViewPayments && hasPayments && (
                <DropdownMenuItem onClick={onViewPayments}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Lihat Semua Pembayaran
                </DropdownMenuItem>
              )}
              {onViewPaymentSchedule && (
                <DropdownMenuItem onClick={onViewPaymentSchedule}>
                  <Calendar className="h-4 w-4 mr-2" />
                  Jadwal Pembayaran
                </DropdownMenuItem>
              )}
              {onManagePaymentTerms && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onManagePaymentTerms}>
                    <FileText className="h-4 w-4 mr-2" />
                    Kelola Termin Pembayaran
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
}
