'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CreditCard, 
  DollarSign, 
  Calendar, 
  AlertTriangle,
  Plus,
  Eye,
  Clock
} from 'lucide-react';
import { PurchaseOrderPaymentSummary } from '@/types/purchase-order';
import { PaymentStatusBadge } from './PaymentStatusBadge';
import { formatCurrency } from '@/lib/utils';

interface PaymentSummaryCardProps {
  paymentSummary: PurchaseOrderPaymentSummary;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function PaymentSummaryCard({
  paymentSummary,
  onViewPayments,
  onAddPayment,
  isLoading = false,
  className,
}: PaymentSummaryCardProps) {
  const {
    totalAmount,
    paidAmount,
    remainingAmount,
    paymentStatus,
    paymentCount,
    lastPaymentDate,
    nextDueDate,
    overdueAmount,
    isOverdue,
  } = paymentSummary;

  const paymentProgress = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 w-32 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
                <div className="h-5 w-24 bg-gray-200 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Ringkasan Pembayaran
            </CardTitle>
            <CardDescription>
              Status pembayaran dan informasi keuangan purchase order
            </CardDescription>
          </div>
          <PaymentStatusBadge status={paymentStatus} />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Payment Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress Pembayaran</span>
            <span>{paymentProgress.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(paymentProgress, 100)}%` }}
            />
          </div>
        </div>

        {/* Payment Amounts */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <DollarSign className="h-3 w-3" />
              Total Amount
            </div>
            <p className="font-semibold">{formatCurrency(totalAmount)}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <DollarSign className="h-3 w-3" />
              Dibayar
            </div>
            <p className="font-semibold text-green-600">{formatCurrency(paidAmount)}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <DollarSign className="h-3 w-3" />
              Sisa
            </div>
            <p className="font-semibold text-blue-600">{formatCurrency(remainingAmount)}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <CreditCard className="h-3 w-3" />
              Jumlah Pembayaran
            </div>
            <p className="font-semibold">{paymentCount}</p>
          </div>
        </div>

        {/* Overdue Warning */}
        {isOverdue && overdueAmount > 0 && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-red-800">
                Pembayaran Terlambat
              </p>
              <p className="text-sm text-red-600">
                {formatCurrency(overdueAmount)} sudah melewati jatuh tempo
              </p>
            </div>
          </div>
        )}

        {/* Payment Dates */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t">
          {lastPaymentDate && (
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Calendar className="h-3 w-3" />
                Pembayaran Terakhir
              </div>
              <p className="text-sm font-medium">
                {new Date(lastPaymentDate).toLocaleDateString('id-ID')}
              </p>
            </div>
          )}
          {nextDueDate && (
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                Jatuh Tempo Berikutnya
              </div>
              <p className="text-sm font-medium">
                {new Date(nextDueDate).toLocaleDateString('id-ID')}
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2 border-t">
          {onViewPayments && (
            <Button
              variant="outline"
              size="sm"
              onClick={onViewPayments}
              className="flex-1"
            >
              <Eye className="h-4 w-4 mr-2" />
              Lihat Pembayaran
            </Button>
          )}
          {onAddPayment && (
            <Button
              size="sm"
              onClick={onAddPayment}
              className="flex-1"
            >
              <Plus className="h-4 w-4 mr-2" />
              Tambah Pembayaran
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
