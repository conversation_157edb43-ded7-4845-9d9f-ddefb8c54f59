import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  ValidateNested,
  IsNumber,
  IsInt,
  Min,
  IsDateString,
  ArrayMinSize,
  IsPositive,
  IsBoolean,
  ValidateIf
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaymentMethod, PurchaseOrderStatus } from '@prisma/client';
import { TaxCalculationOptionsDto } from './tax-configuration.dto';

export class CreatePurchaseOrderItemDto {
  @IsString()
  productId: string;

  @IsString()
  unitId: string;

  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  quantityOrdered: number;

  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  unitPrice: number;

  // Discount Information (item-level)
  @IsOptional()
  @IsString()
  discountType?: string; // PERCENTAGE, FIXED_AMOUNT

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  discountValue?: number;

  @IsOptional()
  @IsDateString()
  expectedDelivery?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  // Selling Price Update (optional)
  @IsOptional()
  @IsBoolean()
  updateSellingPrice?: boolean;

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  newSellingPrice?: number;
}

export class CreatePurchaseOrderDto {
  @IsOptional()
  @IsString()
  orderNumber?: string; // Will be auto-generated if not provided

  @IsString()
  supplierId: string;

  @IsOptional()
  @IsEnum(PurchaseOrderStatus)
  status?: PurchaseOrderStatus;

  @IsOptional()
  @IsDateString()
  orderDate?: string;

  @IsOptional()
  @IsDateString()
  expectedDelivery?: string;

  // Discount Information (order-level)
  @IsOptional()
  @IsString()
  discountType?: string; // PERCENTAGE, FIXED_AMOUNT

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  discountValue?: number;

  // Tax amount - conditional validation based on autoCalculateTax
  @ValidateIf((o) => o.autoCalculateTax === false || o.autoCalculateTax === undefined)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Jumlah pajak harus berupa angka dengan maksimal 2 desimal' })
  @Min(0, { message: 'Jumlah pajak tidak boleh negatif' })
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  taxAmount?: number;

  // Tax Calculation Options
  @IsOptional()
  @IsBoolean({ message: 'Auto kalkulasi pajak harus berupa boolean (true/false)' })
  autoCalculateTax?: boolean; // If true, taxAmount will be calculated automatically

  @IsOptional()
  @IsBoolean()
  taxInclusive?: boolean; // If true, prices include tax; if false, tax is added

  @IsOptional()
  @Type(() => TaxCalculationOptionsDto)
  taxOptions?: TaxCalculationOptionsDto;

  // Payment Information
  @IsOptional()
  @IsInt()
  @Min(0)
  paymentTerms?: number; // Payment terms in days

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  // Delivery Information
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @IsOptional()
  @IsString()
  deliveryContact?: string;

  @IsOptional()
  @IsString()
  deliveryPhone?: string;

  @IsOptional()
  @IsString()
  deliveryNotes?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsString()
  internalNotes?: string;

  // Purchase Order Items
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreatePurchaseOrderItemDto)
  items: CreatePurchaseOrderItemDto[];
}
