import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { PurchaseOrderService } from './services/purchase-order.service';
import { PurchaseOrderPdfService } from './services/purchase-order-pdf.service';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';
import { PurchaseOrderQueryDto, PurchaseOrderStatusUpdateDto } from './dto/purchase-order-query.dto';
import { PurchaseOrderStatsQueryDto } from './dto/purchase-order-stats.dto';
import { CreateSupplierPaymentDto } from '../suppliers/dto/create-supplier-payment.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';

@Controller('purchase-orders')
@UseGuards(JwtAuthGuard)
export class PurchaseOrderController {
  constructor(
    private readonly purchaseOrderService: PurchaseOrderService,
    private readonly purchaseOrderPdfService: PurchaseOrderPdfService,
  ) { }

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createPurchaseOrderDto: CreatePurchaseOrderDto, @Request() req) {
    return this.purchaseOrderService.create(createPurchaseOrderDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: PurchaseOrderQueryDto) {
    return this.purchaseOrderService.findAll(query);
  }

  @Get('stats')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view stats
  async getStats(@Query() query: PurchaseOrderStatsQueryDto) {
    return this.purchaseOrderService.getStats(query.period);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.purchaseOrderService.findOne(id);
  }

  @Get(':id/payment-summary')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view payment summary
  async getPaymentSummary(@Param('id') id: string) {
    return this.purchaseOrderService.getPurchaseOrderPaymentSummary(id);
  }

  @Get(':id/payment-schedule')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view payment schedule
  async getPaymentSchedule(@Param('id') id: string) {
    return this.purchaseOrderService.getPaymentSchedule(id);
  }

  @Get(':id/payment-info')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view payment info
  async getPaymentInfo(@Param('id') id: string) {
    return this.purchaseOrderService.getPurchaseOrderPaymentInfo(id);
  }

  @Get(':id/payments')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can view payments
  async getPayments(
    @Param('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string,
    @Query('paymentMethod') paymentMethod?: string,
    @Query('search') search?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
  ) {
    return this.purchaseOrderService.getPurchaseOrderPayments(id, {
      page,
      limit,
      status,
      paymentMethod,
      search,
      dateFrom,
      dateTo,
    });
  }

  @Post(':id/payments')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create payments
  async createPayment(
    @Param('id') id: string,
    @Body() createPaymentDto: CreateSupplierPaymentDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.createPurchaseOrderPayment(id, createPaymentDto, req.user.id);
  }

  @Patch(':id/payments/:paymentId')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update payments
  async updatePayment(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @Body() updatePaymentDto: Partial<CreateSupplierPaymentDto>,
    @Request() req,
  ) {
    return this.purchaseOrderService.updatePurchaseOrderPayment(id, paymentId, updatePaymentDto, req.user.id);
  }

  @Delete(':id/payments/:paymentId')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can delete payments
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePayment(
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
    @Request() req,
  ) {
    await this.purchaseOrderService.deletePurchaseOrderPayment(id, paymentId, req.user.id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updatePurchaseOrderDto: UpdatePurchaseOrderDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.update(id, updatePurchaseOrderDto, req.user.id);
  }



  @Patch(':id/status')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update status
  async updateStatus(
    @Param('id') id: string,
    @Body() statusDto: PurchaseOrderStatusUpdateDto,
    @Request() req,
  ) {
    return this.purchaseOrderService.updateStatus(id, statusDto, req.user.id);
  }

  @Post(':id/cancel')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can cancel
  async cancel(
    @Param('id') id: string,
    @Body('reason') reason: string,
    @Request() req,
  ) {
    if (!reason) {
      throw new BadRequestException('Alasan pembatalan wajib diisi');
    }
    return this.purchaseOrderService.cancel(id, reason, req.user.id);
  }

  @Post(':id/send')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can send
  async sendToSupplier(
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.purchaseOrderService.sendToSupplier(id, req.user.id);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can hard delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.purchaseOrderService.remove(id);
  }

  @Get(':id/pdf')
  async generatePurchaseOrderPdf(@Param('id') id: string, @Res() res: Response) {
    const pdfBuffer = await this.purchaseOrderPdfService.generatePurchaseOrderPdf(id);
    const purchaseOrder = await this.purchaseOrderService.findOne(id);

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="purchase-order-${purchaseOrder.orderNumber}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });

    res.send(pdfBuffer);
  }

  @Get(':id/receipt')
  async generatePurchaseOrderReceipt(@Param('id') id: string) {
    return this.purchaseOrderPdfService.generatePurchaseOrderReceipt(id);
  }
}
