import { Injectable, NotFoundException, ConflictException, BadRequestException, StreamableFile, InternalServerErrorException } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { SupplierQueryDto } from './dto/supplier-query.dto';
import { CreateSupplierDocumentDto } from './dto/create-supplier-document.dto';
import { CreateSupplierPaymentDto } from './dto/create-supplier-payment.dto';
import { PaymentTermsService } from './services/payment-terms.service';
import { PaymentTermsMonitoringService } from './services/payment-terms-monitoring.service';
import { Prisma, PaymentStatus } from '@prisma/client';
import { FileUploadService } from '../common/services/file-upload.service';
import { ReferenceGeneratorService } from '../common/services/reference-generator.service';
import fs from 'node:fs';

@Injectable()
export class SuppliersService {
  constructor(
    private prisma: PrismaService,
    private fileUploadService: FileUploadService,
    private referenceGeneratorService: ReferenceGeneratorService,
    private paymentTermsService: PaymentTermsService,
    private paymentTermsMonitoringService: PaymentTermsMonitoringService,
    @InjectPinoLogger(SuppliersService.name)
    private readonly logger: PinoLogger,
  ) {}

  async create(createSupplierDto: CreateSupplierDto, userId: string) {
    this.logger.trace({ userId, supplierCode: createSupplierDto.code }, 'Entering create supplier method');

    // Validate required fields
    if (!createSupplierDto.code) {
      this.logger.warn({ userId }, 'Supplier creation failed: missing supplier code');
      throw new BadRequestException('Kode supplier wajib diisi');
    }

    this.logger.debug({
      supplierCode: createSupplierDto.code,
      supplierName: createSupplierDto.name,
      supplierType: createSupplierDto.type,
      hasContacts: !!(createSupplierDto.contacts && createSupplierDto.contacts.length > 0),
      contactsCount: createSupplierDto.contacts?.length || 0,
      userId
    }, 'Validating supplier data for creation');

    // Check if supplier code already exists
    const existingSupplier = await this.prisma.supplier.findUnique({
      where: { code: createSupplierDto.code },
    });

    if (existingSupplier) {
      this.logger.warn({
        supplierCode: createSupplierDto.code,
        existingSupplierId: existingSupplier.id,
        userId
      }, 'Supplier creation failed: code already exists');
      throw new ConflictException('Kode supplier sudah digunakan');
    }

    const { contacts, ...supplierData } = createSupplierDto;

    this.logger.debug({
      supplierCode: createSupplierDto.code,
      contactsCount: contacts?.length || 0,
      hasCreditLimit: !!supplierData.creditLimit,
      userId
    }, 'Creating supplier with contacts');

    try {
      const supplier = await this.prisma.supplier.create({
        data: {
          ...supplierData,
          creditLimit: supplierData.creditLimit ? new Prisma.Decimal(supplierData.creditLimit) : null,
          createdBy: userId,
          updatedBy: userId,
          contacts: contacts ? {
            create: contacts.map(contact => ({
              ...contact,
            }))
          } : undefined,
        },
        include: {
          contacts: true,
          documents: true,
          _count: {
            select: {
              payments: true,
            },
          },
        },
      });

      this.logger.info({
        supplierId: supplier.id,
        supplierCode: supplier.code,
        supplierName: supplier.name,
        supplierType: supplier.type,
        status: supplier.status,
        contactsCount: supplier.contacts.length,
        creditLimit: supplier.creditLimit ? Number(supplier.creditLimit) : null,
        userId
      }, 'Supplier created successfully');

      this.logger.trace({ supplierId: supplier.id }, 'Exiting create supplier method');
      return supplier;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierCode: createSupplierDto.code,
        userId
      }, 'Failed to create supplier');

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Kode supplier sudah digunakan');
        }
      }
      throw error;
    }
  }

  async findAll(query: SupplierQueryDto) {
    this.logger.trace({ query }, 'Entering findAll suppliers method');

    const {
      page = 1,
      limit = 10,
      search,
      type,
      status,
      city,
      province,
      paymentMethod,
      email,
      phone,
      npwp,
      createdBy,
      dateFrom,
      dateTo,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    this.logger.debug({
      page,
      limit,
      search: search ? '***' : undefined,
      type,
      status,
      city,
      province,
      paymentMethod,
      sortBy,
      sortOrder
    }, 'Processing supplier search query');

    const skip = (page - 1) * limit;

    // Build comprehensive where clause
    const where: Prisma.SupplierWhereInput = {
      // Global search across multiple fields
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          { address: { contains: search, mode: 'insensitive' } },
          { npwp: { contains: search, mode: 'insensitive' } },
          { licenseNumber: { contains: search, mode: 'insensitive' } },
        ],
      }),
      // Specific field filters
      ...(type && { type }),
      ...(status && { status }),
      ...(city && { city: { contains: city, mode: 'insensitive' } }),
      ...(province && { province: { contains: province, mode: 'insensitive' } }),
      ...(paymentMethod && { preferredPayment: paymentMethod }),
      ...(email && { email: { contains: email, mode: 'insensitive' } }),
      ...(phone && { phone: { contains: phone, mode: 'insensitive' } }),
      ...(npwp && { npwp: { contains: npwp, mode: 'insensitive' } }),
      ...(createdBy && { createdBy }),
      // Date range filtering
      ...((dateFrom || dateTo) && {
        createdAt: {
          ...(dateFrom && { gte: new Date(dateFrom) }),
          ...(dateTo && { lte: new Date(dateTo) }),
        },
      }),
    };

    // Build order by clause with proper field mapping
    const getOrderByField = (field: string): Prisma.SupplierOrderByWithRelationInput => {
      switch (field) {
        case 'name':
          return { name: sortOrder };
        case 'code':
          return { code: sortOrder };
        case 'type':
          return { type: sortOrder };
        case 'status':
          return { status: sortOrder };
        case 'city':
          return { city: sortOrder };
        case 'province':
          return { province: sortOrder };
        case 'createdAt':
          return { createdAt: sortOrder };
        case 'updatedAt':
          return { updatedAt: sortOrder };
        default:
          return { createdAt: sortOrder };
      }
    };

    const orderBy = getOrderByField(sortBy);

    try {
      const startTime = Date.now();
      const [suppliers, total] = await Promise.all([
        this.prisma.supplier.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            contacts: {
              where: { isPrimary: true },
              take: 1,
            },
            _count: {
              select: {
                documents: {
                  where: { isActive: true },
                },
                payments: true,
              },
            },
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        }),
        this.prisma.supplier.count({ where }),
      ]);
      const queryTime = Date.now() - startTime;

      const result = {
        data: suppliers,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };

      this.logger.info({
        total,
        returned: suppliers.length,
        page,
        limit,
        queryTimeMs: queryTime,
        hasFilters: !!(search || type || status || city || province)
      }, 'Suppliers retrieved successfully');

      this.logger.trace('Exiting findAll suppliers method');
      return result;
    } catch (error) {
      this.logger.error({ err: error, query }, 'Failed to retrieve suppliers');
      throw error;
    }
  }

  async findOne(id: string) {
    this.logger.trace({ supplierId: id }, 'Entering findOne supplier method');

    try {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id },
        include: {
          contacts: {
            orderBy: { isPrimary: 'desc' },
          },
          documents: {
            where: { isActive: true },
            orderBy: { uploadedAt: 'desc' },
          },
          payments: {
            orderBy: { paymentDate: 'desc' },
            take: 10,
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              documents: {
                where: { isActive: true },
              },
              payments: true,
            },
          },
        },
      });

      if (!supplier) {
        this.logger.warn({ supplierId: id }, 'Supplier not found');
        throw new NotFoundException('Supplier tidak ditemukan');
      }

      this.logger.debug({
        supplierId: supplier.id,
        supplierCode: supplier.code,
        supplierName: supplier.name,
        status: supplier.status,
        contactsCount: supplier.contacts.length,
        documentsCount: supplier._count.documents,
        paymentsCount: supplier._count.payments
      }, 'Supplier retrieved successfully');

      this.logger.trace({ supplierId: id }, 'Exiting findOne supplier method');
      return supplier;
    } catch (error) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error({ err: error, supplierId: id }, 'Failed to retrieve supplier');
      }
      throw error;
    }
  }

  async update(id: string, updateSupplierDto: UpdateSupplierDto, userId: string) {
    this.logger.trace({ supplierId: id, userId }, 'Entering update supplier method');

    const supplier = await this.findOne(id);

    this.logger.debug({
      supplierId: id,
      currentCode: supplier.code,
      newCode: updateSupplierDto.code,
      hasContacts: !!(updateSupplierDto.contacts && updateSupplierDto.contacts.length > 0),
      contactsCount: updateSupplierDto.contacts?.length || 0,
      userId
    }, 'Validating supplier update data');

    // Check if code is being changed and if it conflicts
    if (updateSupplierDto.code && updateSupplierDto.code !== supplier.code) {
      const existingSupplier = await this.prisma.supplier.findUnique({
        where: { code: updateSupplierDto.code },
      });

      if (existingSupplier) {
        this.logger.warn({
          supplierId: id,
          newCode: updateSupplierDto.code,
          conflictingSupplierId: existingSupplier.id,
          userId
        }, 'Supplier update failed: code already exists');
        throw new ConflictException('Kode supplier sudah digunakan');
      }
    }

    const { contacts, ...supplierData } = updateSupplierDto;

    this.logger.debug({
      supplierId: id,
      hasContactUpdates: !!(contacts && contacts.length > 0),
      contactsCount: contacts?.length || 0,
      hasCreditLimitUpdate: !!supplierData.creditLimit,
      userId
    }, 'Starting supplier update transaction');

    try {
      // Use transaction to update supplier and contacts atomically
      const updatedSupplier = await this.prisma.$transaction(async (prisma) => {
        // Update supplier data
        const supplier = await prisma.supplier.update({
          where: { id },
          data: {
            ...supplierData,
            creditLimit: supplierData.creditLimit ? new Prisma.Decimal(supplierData.creditLimit) : undefined,
            updatedBy: userId,
          },
        });

        // Handle contacts if provided
        if (contacts && contacts.length > 0) {
          // Process each contact
          for (const contact of contacts) {
            const { id: contactId, isActive, ...contactData } = contact;

            if (contactId) {
              // Update existing contact
              await prisma.supplierContact.update({
                where: { id: contactId },
                data: {
                  ...contactData,
                  isActive: isActive !== undefined ? isActive : true,
                },
              });
            } else {
              // Create new contact
              await prisma.supplierContact.create({
                data: {
                  ...contactData,
                  supplierId: id,
                  isActive: isActive !== undefined ? isActive : true,
                },
              });
            }
          }
        }

        // Return updated supplier with relations
        return prisma.supplier.findUnique({
          where: { id },
          include: {
            contacts: true,
            documents: {
              where: { isActive: true },
            },
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            updatedByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            _count: {
              select: {
                payments: true,
              },
            },
          },
        });
      });

      this.logger.info({
        supplierId: id,
        supplierCode: updatedSupplier?.code,
        supplierName: updatedSupplier?.name,
        contactsCount: updatedSupplier?.contacts?.length || 0,
        userId
      }, 'Supplier updated successfully');

      this.logger.trace({ supplierId: id }, 'Exiting update supplier method');
      return updatedSupplier;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId: id,
        userId
      }, 'Failed to update supplier');

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Kode supplier sudah digunakan');
        }
      }
      throw error;
    }
  }

  async remove(id: string) {
    // Ensure supplier exists
    await this.findOne(id);

    // Soft delete by setting status to INACTIVE
    return this.prisma.supplier.update({
      where: { id },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  async deactivate(id: string, userId: string) {
    this.logger.trace({ supplierId: id, userId }, 'Entering deactivate supplier method');

    // Ensure supplier exists
    const supplier = await this.findOne(id);

    if (supplier.status === 'INACTIVE') {
      this.logger.warn({
        supplierId: id,
        supplierCode: supplier.code,
        userId
      }, 'Supplier deactivation failed: already inactive');
      throw new ConflictException('Supplier sudah dalam status tidak aktif');
    }

    this.logger.debug({
      supplierId: id,
      supplierCode: supplier.code,
      supplierName: supplier.name,
      userId
    }, 'Deactivating supplier');

    try {
      // Soft delete by setting status to INACTIVE
      const deactivatedSupplier = await this.prisma.supplier.update({
        where: { id },
        data: {
          status: 'INACTIVE',
          updatedBy: userId,
          updatedAt: new Date(),
        },
        include: {
          contacts: true,
          documents: {
            where: { isActive: true },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              payments: true,
            },
          },
        },
      });

      this.logger.info({
        supplierId: id,
        supplierCode: supplier.code,
        supplierName: supplier.name,
        userId
      }, 'Supplier deactivated successfully');

      this.logger.trace({ supplierId: id }, 'Exiting deactivate supplier method');
      return deactivatedSupplier;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId: id,
        supplierCode: supplier.code,
        userId
      }, 'Failed to deactivate supplier');
      throw error;
    }
  }

  async activate(id: string, userId: string) {
    this.logger.trace({ supplierId: id, userId }, 'Entering activate supplier method');

    // Ensure supplier exists
    const supplier = await this.findOne(id);

    if (supplier.status === 'ACTIVE') {
      this.logger.warn({
        supplierId: id,
        supplierCode: supplier.code,
        userId
      }, 'Supplier activation failed: already active');
      throw new ConflictException('Supplier sudah dalam status aktif');
    }

    this.logger.debug({
      supplierId: id,
      supplierCode: supplier.code,
      supplierName: supplier.name,
      userId
    }, 'Activating supplier');

    try {
      // Activate by setting status to ACTIVE
      const activatedSupplier = await this.prisma.supplier.update({
        where: { id },
        data: {
          status: 'ACTIVE',
          updatedBy: userId,
          updatedAt: new Date(),
        },
        include: {
          contacts: true,
          documents: {
            where: { isActive: true },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              payments: true,
            },
          },
        },
      });

      this.logger.info({
        supplierId: id,
        supplierCode: supplier.code,
        supplierName: supplier.name,
        userId
      }, 'Supplier activated successfully');

      this.logger.trace({ supplierId: id }, 'Exiting activate supplier method');
      return activatedSupplier;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId: id,
        supplierCode: supplier.code,
        userId
      }, 'Failed to activate supplier');
      throw error;
    }
  }

  async hardDelete(id: string) {
    // Ensure supplier exists
    await this.findOne(id);

    // Check if supplier has any related data that would prevent deletion
    const relatedData = await this.prisma.supplier.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            payments: true,
            documents: true,
            contacts: true,
          },
        },
      },
    });

    if (!relatedData) {
      throw new NotFoundException('Supplier tidak ditemukan');
    }

    if (relatedData._count.payments > 0) {
      throw new ConflictException('Tidak dapat menghapus supplier yang memiliki data pembayaran');
    }

    // Use transaction to ensure all related data is deleted
    await this.prisma.$transaction(async (prisma) => {
      // Delete related documents first
      if (relatedData._count.documents > 0) {
        await prisma.supplierDocument.deleteMany({
          where: { supplierId: id },
        });
      }

      // Delete related contacts
      if (relatedData._count.contacts > 0) {
        await prisma.supplierContact.deleteMany({
          where: { supplierId: id },
        });
      }

      // Finally delete the supplier
      await prisma.supplier.delete({
        where: { id },
      });
    });
  }

  // Document management
  async getDocuments(supplierId: string, options?: {
    page?: number;
    limit?: number;
    type?: string;
    search?: string;
  }) {
    await this.findOne(supplierId); // Ensure supplier exists

    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;

    const where: any = {
      supplierId,
      isActive: true,
      type: { not: 'PAYMENT_PROOF' }, // Exclude payment proofs from regular document listings
    };

    if (options?.type) {
      where.type = options.type;
    }

    if (options?.search) {
      where.OR = [
        { name: { contains: options.search, mode: 'insensitive' } },
        { description: { contains: options.search, mode: 'insensitive' } },
        { fileName: { contains: options.search, mode: 'insensitive' } },
      ];
    }

    const [documents, total] = await Promise.all([
      this.prisma.supplierDocument.findMany({
        where,
        skip,
        take: limit,
        orderBy: { uploadedAt: 'desc' },
      }),
      this.prisma.supplierDocument.count({ where }),
    ]);

    return {
      data: documents,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async getDocument(supplierId: string, documentId: string) {
    await this.findOne(supplierId); // Ensure supplier exists

    const document = await this.prisma.supplierDocument.findFirst({
      where: {
        id: documentId,
        supplierId,
        isActive: true,
      },
    });

    if (!document) {
      throw new NotFoundException('Dokumen tidak ditemukan');
    }

    return document;
  }

  async downloadDocument(supplierId: string, documentId: string) {
    const document = await this.getDocument(supplierId, documentId);

    if (!document.filePath) {
      throw new NotFoundException('File tidak ditemukan');
    }

    const filePath = document.filePath.startsWith('/') ? `.${document.filePath}` : document.filePath;
    const fileStream = fs.createReadStream(filePath);
    return new StreamableFile(fileStream, {
      type: document.mimeType || 'application/octet-stream',
      length: document.fileSize || undefined,
    });
  }

  async createDocument(supplierId: string, createDocumentDto: CreateSupplierDocumentDto, userId: string) {
    this.logger.trace({ supplierId, userId }, 'Entering createDocument method');

    await this.findOne(supplierId); // Ensure supplier exists

    if (!createDocumentDto) {
      this.logger.warn({ supplierId, userId }, 'Document creation failed: missing document data');
      throw new BadRequestException('Data dokumen wajib diisi');
    }

    this.logger.debug({
      supplierId,
      documentType: createDocumentDto.type,
      documentName: createDocumentDto.name,
      fileName: createDocumentDto.fileName,
      fileSize: createDocumentDto.fileSize,
      userId
    }, 'Creating supplier document');

    try {
      const document = await this.prisma.supplierDocument.create({
        data: {
          ...createDocumentDto,
          supplierId,
          expiryDate: createDocumentDto.expiryDate ? new Date(createDocumentDto.expiryDate) : null,
          uploadedBy: userId,
        },
      });

      this.logger.info({
        supplierId,
        documentId: document.id,
        documentType: document.type,
        documentName: document.name,
        fileName: document.fileName,
        userId
      }, 'Supplier document created successfully');

      this.logger.trace({ supplierId, documentId: document.id }, 'Exiting createDocument method');
      return document;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        documentType: createDocumentDto.type,
        userId
      }, 'Failed to create supplier document');
      throw error;
    }
  }

  async updateDocument(
    supplierId: string,
    documentId: string,
    updateDocumentDto: Partial<CreateSupplierDocumentDto>,
    userId: string,
  ) {
    await this.getDocument(supplierId, documentId); // Ensure document exists

    // Handle expiryDate properly
    let expiryDate: Date | null | undefined = undefined;
    if (updateDocumentDto.expiryDate !== undefined) {
      if (updateDocumentDto.expiryDate === null || updateDocumentDto.expiryDate === '') {
        expiryDate = null;
      } else {
        const date = new Date(updateDocumentDto.expiryDate);
        if (isNaN(date.getTime())) {
          throw new BadRequestException('Format tanggal kedaluwarsa tidak valid');
        }
        expiryDate = date;
      }
    }

    return this.prisma.supplierDocument.update({
      where: { id: documentId },
      data: {
        ...updateDocumentDto,
        expiryDate,
      },
    });
  }

  async deleteDocument(supplierId: string, documentId: string, userId: string) {
    await this.getDocument(supplierId, documentId); // Ensure document exists

    // Soft delete
    await this.prisma.supplierDocument.update({
      where: { id: documentId },
      data: {
        isActive: false,
      },
    });
  }

  async deleteMultipleDocuments(supplierId: string, documentIds: string[], userId: string) {
    await this.findOne(supplierId); // Ensure supplier exists

    // Soft delete multiple documents
    await this.prisma.supplierDocument.updateMany({
      where: {
        id: { in: documentIds },
        supplierId,
        isActive: true,
      },
      data: {
        isActive: false,
      },
    });
  }

  // Payment management
  async getPayments(supplierId: string, options?: {
    page?: number;
    limit?: number;
    status?: string;
    paymentMethod?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
  }) {
    await this.findOne(supplierId); // Ensure supplier exists

    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;

    const where: any = { supplierId };

    if (options?.status) {
      where.status = options.status;
    }

    if (options?.paymentMethod) {
      where.paymentMethod = options.paymentMethod;
    }

    if (options?.search) {
      where.OR = [
        { invoiceNumber: { contains: options.search, mode: 'insensitive' } },
        { reference: { contains: options.search, mode: 'insensitive' } },
        { notes: { contains: options.search, mode: 'insensitive' } },
      ];
    }

    if (options?.dateFrom || options?.dateTo) {
      where.paymentDate = {};
      if (options.dateFrom) {
        where.paymentDate.gte = new Date(options.dateFrom);
      }
      if (options.dateTo) {
        where.paymentDate.lte = new Date(options.dateTo);
      }
    }

    const [payments, total] = await Promise.all([
      this.prisma.supplierPayment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { paymentDate: 'desc' },
      }),
      this.prisma.supplierPayment.count({ where }),
    ]);

    return {
      data: payments.map(payment => ({
        ...payment,
        amount: Number(payment.amount),
      })),
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async getPayment(supplierId: string, paymentId: string) {
    await this.findOne(supplierId); // Ensure supplier exists

    const payment = await this.prisma.supplierPayment.findFirst({
      where: {
        id: paymentId,
        supplierId,
      },
      include: {
        purchaseOrder: true,
        goodsReceipt: true,
      },
    });

    if (!payment) {
      throw new NotFoundException('Pembayaran tidak ditemukan');
    }

    return {
      ...payment,
      amount: Number(payment.amount),
    };
  }

  async getPaymentSummary(supplierId: string) {
    await this.findOne(supplierId); // Ensure supplier exists

    const [
      totalPaid,
      totalPending,
      totalOverdue,
      recentPayments,
      paymentsByMethod,
      paymentsByMonth,
    ] = await Promise.all([
      this.prisma.supplierPayment.aggregate({
        where: { supplierId, status: 'PAID' },
        _sum: { amount: true },
        _count: true,
      }),
      this.prisma.supplierPayment.aggregate({
        where: { supplierId, status: 'PENDING' },
        _sum: { amount: true },
        _count: true,
      }),
      this.prisma.supplierPayment.aggregate({
        where: {
          supplierId,
          OR: [
            { status: 'OVERDUE' },
            {
              AND: [
                { status: { not: 'PAID' } }, // Exclude PAID payments
                { status: { not: 'CANCELLED' } }, // Exclude CANCELLED payments
                { dueDate: { lt: new Date() } }, // Past due date
              ],
            },
          ],
        },
        _sum: { amount: true },
        _count: true,
      }),
      this.prisma.supplierPayment.findMany({
        where: { supplierId },
        orderBy: { paymentDate: 'desc' },
        take: 5,
      }),
      this.prisma.supplierPayment.groupBy({
        by: ['paymentMethod'],
        where: { supplierId },
        _sum: { amount: true },
        _count: true,
      }),
      this.prisma.supplierPayment.groupBy({
        by: ['paymentDate'],
        where: {
          supplierId,
          paymentDate: {
            gte: new Date(new Date().getFullYear(), 0, 1), // This year
          },
        },
        _sum: { amount: true },
        _count: true,
      }),
    ]);

    return {
      summary: {
        totalPaid: totalPaid._sum.amount ? Number(totalPaid._sum.amount) : 0,
        totalPending: totalPending._sum.amount ? Number(totalPending._sum.amount) : 0,
        totalOverdue: totalOverdue._sum.amount ? Number(totalOverdue._sum.amount) : 0,
        countPaid: totalPaid._count,
        countPending: totalPending._count,
        countOverdue: totalOverdue._count,
      },
      recentPayments: recentPayments.map(payment => ({
        ...payment,
        amount: Number(payment.amount),
      })),
      paymentsByMethod: paymentsByMethod.map(item => ({
        method: item.paymentMethod,
        amount: item._sum.amount ? Number(item._sum.amount) : 0,
        count: item._count,
      })),
      paymentsByMonth: paymentsByMonth.map(item => ({
        date: item.paymentDate,
        amount: item._sum.amount ? Number(item._sum.amount) : 0,
        count: item._count,
      })),
    };
  }

  async createPayment(supplierId: string, createPaymentDto: CreateSupplierPaymentDto, userId: string) {
    this.logger.trace({ supplierId, userId }, 'Entering createPayment method');

    await this.findOne(supplierId); // Ensure supplier exists

    this.logger.debug({
      supplierId,
      purchaseOrderId: createPaymentDto.purchaseOrderId,
      goodsReceiptId: createPaymentDto.goodsReceiptId,
      amount: createPaymentDto.amount,
      paymentMethod: createPaymentDto.paymentMethod,
      status: createPaymentDto.status,
      proposedReference: createPaymentDto.reference,
      userId
    }, 'Creating supplier payment');

    try {
      // Validate business rules first
      await this.validatePaymentBusinessRules(createPaymentDto, supplierId);

      // Validate purchase order if provided
      let purchaseOrder: any = null;
      if (createPaymentDto.purchaseOrderId) {
        purchaseOrder = await this.prisma.purchaseOrder.findUnique({
          where: { id: createPaymentDto.purchaseOrderId },
          include: { supplier: true }
        });

        if (!purchaseOrder) {
          this.logger.warn({
            supplierId,
            purchaseOrderId: createPaymentDto.purchaseOrderId,
            userId
          }, 'Purchase order not found for payment creation');
          throw new BadRequestException('Purchase order tidak ditemukan');
        }

        if (purchaseOrder.supplierId !== supplierId) {
          this.logger.warn({
            supplierId,
            purchaseOrderSupplierId: purchaseOrder.supplierId,
            purchaseOrderId: createPaymentDto.purchaseOrderId,
            userId
          }, 'Purchase order supplier mismatch');
          throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
        }

        this.logger.debug({
          supplierId,
          purchaseOrderId: createPaymentDto.purchaseOrderId,
          purchaseOrderStatus: purchaseOrder.status,
          purchaseOrderAmount: Number(purchaseOrder.totalAmount),
          userId
        }, 'Purchase order validated for payment');
      }

      // Validate goods receipt if provided
      let goodsReceipt: any = null;
      if (createPaymentDto.goodsReceiptId) {
        goodsReceipt = await this.prisma.goodsReceipt.findUnique({
          where: { id: createPaymentDto.goodsReceiptId },
          include: { supplier: true }
        });

        if (!goodsReceipt) {
          this.logger.warn({
            supplierId,
            goodsReceiptId: createPaymentDto.goodsReceiptId,
            userId
          }, 'Goods receipt not found for payment creation');
          throw new BadRequestException('Goods receipt tidak ditemukan');
        }

        if (goodsReceipt.supplierId !== supplierId) {
          this.logger.warn({
            supplierId,
            goodsReceiptSupplierId: goodsReceipt.supplierId,
            goodsReceiptId: createPaymentDto.goodsReceiptId,
            userId
          }, 'Goods receipt supplier mismatch');
          throw new BadRequestException('Goods receipt tidak sesuai dengan supplier');
        }

        this.logger.debug({
          supplierId,
          goodsReceiptId: createPaymentDto.goodsReceiptId,
          goodsReceiptStatus: goodsReceipt.status,
          goodsReceiptAmount: Number(goodsReceipt.totalAmount),
          userId
        }, 'Goods receipt validated for payment');
      }

      // Calculate payment terms and due date
      const paymentTermsCalc = await this.paymentTermsService.calculatePaymentTerms(
        supplierId,
        createPaymentDto.purchaseOrderId,
        createPaymentDto.effectivePaymentTerms,
      );

      // Use calculated due date if not provided
      const finalDueDate = createPaymentDto.dueDate
        ? new Date(createPaymentDto.dueDate)
        : paymentTermsCalc.calculatedDueDate;

      this.logger.debug({
        supplierId,
        purchaseOrderId: createPaymentDto.purchaseOrderId,
        paymentTermsCalculation: {
          effectivePaymentTerms: paymentTermsCalc.effectivePaymentTerms,
          calculatedDueDate: paymentTermsCalc.calculatedDueDate,
          isOverdue: paymentTermsCalc.isOverdue,
          source: paymentTermsCalc.source
        },
        finalDueDate,
        userId
      }, 'Payment terms calculated for payment creation');

      // Generate or validate reference number
      const reference = await this.referenceGeneratorService.ensureUniqueReference(createPaymentDto.reference);

      if (reference !== createPaymentDto.reference) {
        this.logger.debug({
          supplierId,
          proposedReference: createPaymentDto.reference,
          generatedReference: reference,
          userId
        }, 'Payment reference was generated/modified for uniqueness');
      }

      // Create payment and update related entities in a transaction with comprehensive error handling
      const transactionStartTime = Date.now();
      const transactionId = `payment_create_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      this.logger.info({
        transactionId,
        supplierId,
        amount: createPaymentDto.amount,
        purchaseOrderId: createPaymentDto.purchaseOrderId,
        goodsReceiptId: createPaymentDto.goodsReceiptId
      }, 'Starting payment creation transaction');

      const result = await this.prisma.$transaction(async (prisma) => {
        try {
          // Pre-transaction validation within transaction scope
          await this.validatePaymentCreationPreconditions(
            prisma,
            createPaymentDto,
            supplierId,
            transactionId
          );

          // Create the payment with optimistic locking considerations
          const payment = await prisma.supplierPayment.create({
            data: {
              ...createPaymentDto,
              supplierId,
              reference, // Use the generated/validated reference
              amount: new Prisma.Decimal(createPaymentDto.amount),
              paymentDate: new Date(createPaymentDto.paymentDate),
              dueDate: finalDueDate,
              effectivePaymentTerms: paymentTermsCalc.effectivePaymentTerms,
              createdBy: userId,
            },
            include: {
              supplier: true,
              purchaseOrder: true,
              goodsReceipt: true,
            },
          });

          this.logger.debug({
            transactionId,
            paymentId: payment.id,
            reference: payment.reference,
            amount: Number(payment.amount)
          }, 'Payment record created successfully in transaction');

          // Update purchase order payment status if linked
          if (createPaymentDto.purchaseOrderId && purchaseOrder) {
            await this.updatePurchaseOrderPaymentStatusWithErrorHandling(
              prisma,
              createPaymentDto.purchaseOrderId,
              userId,
              transactionId
            );
          }

          // Post-creation validation
          await this.validatePaymentCreationResults(prisma, payment, transactionId);

          this.logger.info({
            transactionId,
            paymentId: payment.id,
            supplierId,
            amount: Number(payment.amount),
            effectivePaymentTerms: paymentTermsCalc.effectivePaymentTerms,
            purchaseOrderId: payment.purchaseOrderId,
            duration: Date.now() - transactionStartTime
          }, 'Payment creation transaction completed successfully');

          return payment;

        } catch (transactionError) {
          this.logger.error({
            err: transactionError,
            transactionId,
            supplierId,
            amount: createPaymentDto.amount,
            purchaseOrderId: createPaymentDto.purchaseOrderId,
            duration: Date.now() - transactionStartTime
          }, 'Payment creation transaction failed - rolling back all changes');

          // Re-throw to trigger transaction rollback
          throw transactionError;
        }
      }, {
        timeout: 15000, // 15 second timeout for payment creation
        isolationLevel: 'ReadCommitted'
      });

      // EXTERNAL OPERATIONS (after successful transaction commit)
      try {
        // Log compliance event (external operation - not in transaction)
        await this.paymentTermsMonitoringService.logComplianceEvent(
          'PAYMENT_CREATED',
          supplierId,
          {
            paymentId: result.id,
            amount: Number(result.amount),
            effectivePaymentTerms: paymentTermsCalc.effectivePaymentTerms,
            isOverdue: paymentTermsCalc.isOverdue,
            daysOverdue: paymentTermsCalc.daysOverdue,
            purchaseOrderId: result.purchaseOrderId,
            paymentMethod: result.paymentMethod,
            userId
          }
        );
      } catch (complianceError) {
        // Log compliance logging failure but don't fail the payment creation
        this.logger.warn({
          err: complianceError,
          paymentId: result.id,
          supplierId,
          userId
        }, 'Failed to log compliance event after successful payment creation');
      }

      this.logger.info({
        supplierId,
        paymentId: result.id,
        reference: result.reference,
        amount: Number(result.amount),
        paymentMethod: result.paymentMethod,
        status: result.status,
        purchaseOrderId: result.purchaseOrderId,
        goodsReceiptId: result.goodsReceiptId,
        effectivePaymentTerms: paymentTermsCalc.effectivePaymentTerms,
        isOverdue: paymentTermsCalc.isOverdue,
        userId
      }, 'Supplier payment created successfully with compliance tracking');

      this.logger.trace({ supplierId, paymentId: result.id }, 'Exiting createPayment method');
      return {
        ...result,
        amount: Number(result.amount),
      };
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        amount: createPaymentDto.amount,
        purchaseOrderId: createPaymentDto.purchaseOrderId,
        goodsReceiptId: createPaymentDto.goodsReceiptId,
        userId
      }, 'Failed to create supplier payment');
      throw error;
    }
  }

  async updatePayment(
    supplierId: string,
    paymentId: string,
    updatePaymentDto: Partial<CreateSupplierPaymentDto>,
    userId: string,
  ) {
    this.logger.trace({ supplierId, paymentId, userId }, 'Entering updatePayment method');

    const existingPayment = await this.getPayment(supplierId, paymentId); // Ensure payment exists

    this.logger.debug({
      supplierId,
      paymentId,
      updateData: {
        purchaseOrderId: updatePaymentDto.purchaseOrderId,
        goodsReceiptId: updatePaymentDto.goodsReceiptId,
        amount: updatePaymentDto.amount,
        status: updatePaymentDto.status
      },
      userId
    }, 'Updating supplier payment');

    try {
      // Validate new purchase order if provided
      if (updatePaymentDto.purchaseOrderId && updatePaymentDto.purchaseOrderId !== existingPayment.purchaseOrderId) {
        const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
          where: { id: updatePaymentDto.purchaseOrderId }
        });

        if (!purchaseOrder) {
          throw new BadRequestException('Purchase order tidak ditemukan');
        }

        if (purchaseOrder.supplierId !== supplierId) {
          throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
        }
      }

      // Validate new goods receipt if provided
      if (updatePaymentDto.goodsReceiptId && updatePaymentDto.goodsReceiptId !== existingPayment.goodsReceiptId) {
        const goodsReceipt = await this.prisma.goodsReceipt.findUnique({
          where: { id: updatePaymentDto.goodsReceiptId }
        });

        if (!goodsReceipt) {
          throw new BadRequestException('Goods receipt tidak ditemukan');
        }

        if (goodsReceipt.supplierId !== supplierId) {
          throw new BadRequestException('Goods receipt tidak sesuai dengan supplier');
        }
      }

      // Update payment and related entities in a transaction
      const result = await this.prisma.$transaction(async (prisma) => {
        const payment = await prisma.supplierPayment.update({
          where: { id: paymentId },
          data: {
            ...updatePaymentDto,
            amount: updatePaymentDto.amount ? new Prisma.Decimal(updatePaymentDto.amount) : undefined,
            paymentDate: updatePaymentDto.paymentDate ? new Date(updatePaymentDto.paymentDate) : undefined,
            dueDate: updatePaymentDto.dueDate ? new Date(updatePaymentDto.dueDate) : undefined,
          },
          include: {
            supplier: true,
            purchaseOrder: true,
            goodsReceipt: true,
          },
        });

        // Update purchase order payment status if linked (both old and new)
        const purchaseOrderIds = [
          existingPayment.purchaseOrderId,
          payment.purchaseOrderId
        ].filter(Boolean);

        for (const poId of purchaseOrderIds) {
          if (poId) {
            await this.updatePurchaseOrderPaymentStatus(prisma, poId, userId);
          }
        }

        return payment;
      });

      this.logger.info({
        supplierId,
        paymentId,
        oldPurchaseOrderId: existingPayment.purchaseOrderId,
        newPurchaseOrderId: result.purchaseOrderId,
        oldGoodsReceiptId: existingPayment.goodsReceiptId,
        newGoodsReceiptId: result.goodsReceiptId,
        oldAmount: Number(existingPayment.amount),
        newAmount: Number(result.amount),
        oldStatus: existingPayment.status,
        newStatus: result.status,
        userId
      }, 'Supplier payment updated successfully');

      this.logger.trace({ supplierId, paymentId }, 'Exiting updatePayment method');
      return {
        ...result,
        amount: Number(result.amount),
      };
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        paymentId,
        updateData: updatePaymentDto,
        userId
      }, 'Failed to update supplier payment');
      throw error;
    }
  }

  async deletePayment(supplierId: string, paymentId: string, userId: string) {
    this.logger.trace({ supplierId, paymentId, userId }, 'Entering deletePayment method');

    const existingPayment = await this.getPayment(supplierId, paymentId); // Ensure payment exists

    this.logger.debug({
      supplierId,
      paymentId,
      paymentAmount: Number(existingPayment.amount),
      paymentStatus: existingPayment.status,
      purchaseOrderId: existingPayment.purchaseOrderId,
      goodsReceiptId: existingPayment.goodsReceiptId,
      userId
    }, 'Deleting supplier payment');

    try {
      // Delete payment and update related entities in a transaction
      await this.prisma.$transaction(async (prisma) => {
        // Delete the payment
        await prisma.supplierPayment.delete({
          where: { id: paymentId },
        });

        // Update purchase order payment status if linked
        if (existingPayment.purchaseOrderId) {
          await this.updatePurchaseOrderPaymentStatus(prisma, existingPayment.purchaseOrderId, userId);
        }
      });

      this.logger.info({
        supplierId,
        paymentId,
        deletedAmount: Number(existingPayment.amount),
        purchaseOrderId: existingPayment.purchaseOrderId,
        goodsReceiptId: existingPayment.goodsReceiptId,
        userId
      }, 'Supplier payment deleted successfully');

      this.logger.trace({ supplierId, paymentId }, 'Exiting deletePayment method');
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        paymentId,
        userId
      }, 'Failed to delete supplier payment');
      throw error;
    }
  }

  // Payment reference number generation
  async generatePaymentReference(): Promise<string> {
    return this.referenceGeneratorService.generatePaymentReference();
  }

  async validatePaymentReference(reference: string): Promise<boolean> {
    return this.referenceGeneratorService.validateReferenceUniqueness(reference);
  }

  // Payment Proof management
  async getPaymentProof(supplierId: string, paymentId: string) {
    await this.findOne(supplierId); // Ensure supplier exists
    await this.getPayment(supplierId, paymentId); // Ensure payment exists

    const paymentProof = await this.prisma.supplierDocument.findFirst({
      where: {
        supplierId,
        paymentId,
        type: 'PAYMENT_PROOF' as any,
        isActive: true,
      },
    });

    return paymentProof;
  }

  async createPaymentProof(
    supplierId: string,
    paymentId: string,
    createPaymentProofDto: any,
    userId: string,
  ) {
    await this.findOne(supplierId); // Ensure supplier exists
    await this.getPayment(supplierId, paymentId); // Ensure payment exists

    // Check if payment proof already exists
    const existingProof = await this.getPaymentProof(supplierId, paymentId);
    if (existingProof) {
      throw new BadRequestException('Bukti pembayaran sudah ada untuk pembayaran ini');
    }

    return this.prisma.supplierDocument.create({
      data: {
        ...createPaymentProofDto,
        supplierId,
        paymentId,
        type: 'PAYMENT_PROOF' as any,
        uploadedBy: userId,
      },
    });
  }

  async deletePaymentProof(supplierId: string, paymentId: string, userId: string) {
    // Get the payment proof to delete
    const paymentProof = await this.getPaymentProof(supplierId, paymentId);

    if (!paymentProof) {
      throw new NotFoundException('Bukti pembayaran tidak ditemukan');
    }

    try {
      // Use transaction to ensure both database and file operations succeed or fail together
      await this.prisma.$transaction(async (prisma) => {
        // Hard delete from database (permanent removal)
        await prisma.supplierDocument.delete({
          where: { id: paymentProof.id },
        });

        // Delete the actual file from filesystem
        if (paymentProof.filePath) {
          // Convert URL path to filesystem path more robustly
          let filePath = paymentProof.filePath;

          // Handle different path formats:
          // 1. URL path: /uploads/payment-proofs/file.pdf -> ./uploads/payment-proofs/file.pdf
          // 2. Relative path: uploads/payment-proofs/file.pdf -> ./uploads/payment-proofs/file.pdf
          // 3. Absolute path: ./uploads/payment-proofs/file.pdf -> ./uploads/payment-proofs/file.pdf

          if (filePath.startsWith('/uploads/')) {
            filePath = `.${filePath}`;
          } else if (filePath.startsWith('uploads/')) {
            filePath = `./${filePath}`;
          } else if (!filePath.startsWith('./')) {
            // Fallback: assume it's a relative path from project root
            filePath = `./${filePath}`;
          }

          try {
            console.log(`Attempting to delete file: ${filePath} (original: ${paymentProof.filePath})`);

            // Use FileUploadService for consistent file handling
            this.fileUploadService.deleteFile(filePath);

            console.log(`File deleted successfully: ${filePath}`);
          } catch (fileError) {
            console.warn(`Warning: Could not delete file ${paymentProof.filePath} (converted to ${filePath}):`, fileError);
            // Don't throw error for file deletion failure - database deletion is more important
            // File cleanup can be handled separately if needed
          }
        }
      });

      console.log(`Payment proof deleted successfully: ${paymentProof.id} for payment ${paymentId}`);
    } catch (error) {
      console.error('Error deleting payment proof:', error);

      // Re-throw specific errors
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle Prisma errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException('Bukti pembayaran tidak ditemukan');
        }
      }

      throw new InternalServerErrorException('Gagal menghapus bukti pembayaran');
    }
  }

  // Statistics
  async getStats() {
    const [total, active, inactive, byType] = await Promise.all([
      this.prisma.supplier.count(),
      this.prisma.supplier.count({ where: { status: 'ACTIVE' } }),
      this.prisma.supplier.count({ where: { status: 'INACTIVE' } }),
      this.prisma.supplier.groupBy({
        by: ['type'],
        _count: true,
      }),
    ]);

    return {
      total,
      active,
      inactive,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = item._count;
        return acc;
      }, {}),
    };
  }

  // Business logic validation for payments
  private async validatePaymentBusinessRules(createPaymentDto: CreateSupplierPaymentDto, supplierId: string) {
    this.logger.trace({ supplierId }, 'Validating payment business rules');

    // Rule 1: Cannot create payment for cancelled purchase orders
    if (createPaymentDto.purchaseOrderId) {
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: createPaymentDto.purchaseOrderId },
        select: {
          status: true,
          totalAmount: true,
          orderDate: true,
          paymentTerms: true,
          paymentStatus: true
        }
      });

      if (purchaseOrder?.status === 'CANCELLED') {
        throw new BadRequestException('Tidak dapat membuat pembayaran untuk purchase order yang dibatalkan');
      }

      // Rule 2: Payment amount should not exceed purchase order total
      if (purchaseOrder && Number(createPaymentDto.amount) > Number(purchaseOrder.totalAmount)) {
        this.logger.warn({
          supplierId,
          purchaseOrderId: createPaymentDto.purchaseOrderId,
          paymentAmount: createPaymentDto.amount,
          orderTotal: Number(purchaseOrder.totalAmount)
        }, 'Payment amount exceeds purchase order total');

        throw new BadRequestException('Jumlah pembayaran tidak boleh melebihi total purchase order');
      }

      // Rule 3: Validate payment terms compliance
      if (purchaseOrder) {
        const paymentTermsCalc = await this.paymentTermsService.calculatePaymentTerms(
          supplierId,
          createPaymentDto.purchaseOrderId,
          createPaymentDto.effectivePaymentTerms,
        );

        // Check if payment is being made too early (before goods receipt for credit terms)
        if (paymentTermsCalc.effectivePaymentTerms > 0) {
          const goodsReceipts = await this.prisma.goodsReceipt.findMany({
            where: { purchaseOrderId: createPaymentDto.purchaseOrderId },
            select: { status: true, receiptDate: true }
          });

          const hasCompletedReceipt = goodsReceipts.some(gr => gr.status === 'COMPLETED');

          if (!hasCompletedReceipt) {
            this.logger.warn({
              supplierId,
              purchaseOrderId: createPaymentDto.purchaseOrderId,
              paymentTerms: paymentTermsCalc.effectivePaymentTerms
            }, 'Payment being made before goods receipt completion for credit terms');

            // This is a warning, not an error - allow early payment but log it
            this.logger.info({
              supplierId,
              purchaseOrderId: createPaymentDto.purchaseOrderId,
              message: 'Early payment made before goods receipt completion'
            }, 'Early payment allowed for credit terms');
          }
        }

        // Rule 4: Check for overdue payment penalties (Indonesian pharmacy context)
        if (paymentTermsCalc.isOverdue && paymentTermsCalc.daysOverdue > 30) {
          this.logger.warn({
            supplierId,
            purchaseOrderId: createPaymentDto.purchaseOrderId,
            daysOverdue: paymentTermsCalc.daysOverdue,
            message: 'Payment significantly overdue - may incur penalties'
          }, 'Significantly overdue payment detected');

          // Log for potential penalty calculation but don't block payment
        }
      }
    }

    // Rule 3: Cannot create payment for completed goods receipts with status REJECTED
    if (createPaymentDto.goodsReceiptId) {
      const goodsReceipt = await this.prisma.goodsReceipt.findUnique({
        where: { id: createPaymentDto.goodsReceiptId },
        select: { status: true, totalAmount: true }
      });

      if (goodsReceipt?.status === 'REJECTED') {
        throw new BadRequestException('Tidak dapat membuat pembayaran untuk goods receipt yang ditolak');
      }

      // Rule 4: Payment amount should not exceed goods receipt total
      if (goodsReceipt && Number(createPaymentDto.amount) > Number(goodsReceipt.totalAmount)) {
        this.logger.warn({
          supplierId,
          goodsReceiptId: createPaymentDto.goodsReceiptId,
          paymentAmount: createPaymentDto.amount,
          receiptTotal: Number(goodsReceipt.totalAmount)
        }, 'Payment amount exceeds goods receipt total');

        throw new BadRequestException('Jumlah pembayaran tidak boleh melebihi total goods receipt');
      }
    }

    // Rule 5: Payment date should not be in the future
    const paymentDate = new Date(createPaymentDto.paymentDate);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    if (paymentDate > today) {
      throw new BadRequestException('Tanggal pembayaran tidak boleh di masa depan');
    }

    // Rule 6: Due date should be after payment date if provided
    if (createPaymentDto.dueDate) {
      const dueDate = new Date(createPaymentDto.dueDate);
      if (dueDate < paymentDate) {
        throw new BadRequestException('Tanggal jatuh tempo harus setelah tanggal pembayaran');
      }
    }

    // Rule 7: Validate payment terms consistency
    if (createPaymentDto.effectivePaymentTerms !== undefined) {
      if (createPaymentDto.effectivePaymentTerms < 0) {
        throw new BadRequestException('Termin pembayaran tidak boleh negatif');
      }

      // Check if manual payment terms are reasonable (max 365 days for Indonesian pharmacy)
      if (createPaymentDto.effectivePaymentTerms > 365) {
        throw new BadRequestException('Termin pembayaran tidak boleh lebih dari 365 hari');
      }

      // Warn if payment terms are unusually long for pharmacy business
      if (createPaymentDto.effectivePaymentTerms > 90) {
        this.logger.warn({
          supplierId,
          effectivePaymentTerms: createPaymentDto.effectivePaymentTerms,
          message: 'Unusually long payment terms for pharmacy business'
        }, 'Long payment terms detected');
      }
    }

    // Rule 8: Check supplier credit limit compliance (if applicable)
    const supplier = await this.prisma.supplier.findUnique({
      where: { id: supplierId },
      select: {
        creditLimit: true,
        paymentTerms: true
      }
    });

    if (supplier?.creditLimit && createPaymentDto.purchaseOrderId) {
      // Get total outstanding amount for this supplier
      const outstandingOrders = await this.prisma.purchaseOrder.findMany({
        where: {
          supplierId,
          paymentStatus: {
            in: ['PENDING', 'PARTIAL', 'OVERDUE']
          }
        },
        include: {
          payments: {
            where: {
              status: {
                in: ['PAID', 'PARTIAL']
              }
            },
            select: { amount: true }
          }
        }
      });

      const totalOutstanding = outstandingOrders.reduce((sum, order) => {
        const totalPaid = order.payments.reduce((paidSum, payment) =>
          paidSum + Number(payment.amount), 0);
        return sum + (Number(order.totalAmount) - totalPaid);
      }, 0);

      if (totalOutstanding > Number(supplier.creditLimit)) {
        this.logger.warn({
          supplierId,
          totalOutstanding,
          creditLimit: Number(supplier.creditLimit),
          message: 'Supplier credit limit exceeded'
        }, 'Credit limit exceeded for supplier');

        // This is a warning for business review, not a blocking error
      }
    }

    this.logger.debug({ supplierId }, 'Payment business rules validation passed');
  }

  // Helper method to update purchase order payment status based on payments
  private async updatePurchaseOrderPaymentStatus(prisma: any, purchaseOrderId: string, userId: string) {
    this.logger.trace({ purchaseOrderId, userId }, 'Updating purchase order payment status');

    try {
      // Get purchase order with payments
      const purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          payments: {
            where: {
              status: {
                in: ['PAID', 'PARTIAL']
              }
            }
          }
        }
      });

      if (!purchaseOrder) {
        this.logger.warn({ purchaseOrderId, userId }, 'Purchase order not found for payment status update');
        return;
      }

      // Calculate total paid amount
      const totalPaid = purchaseOrder.payments.reduce((sum: number, payment: any) => {
        return sum + Number(payment.amount);
      }, 0);

      const totalAmount = Number(purchaseOrder.totalAmount);

      // Determine new payment status
      let newPaymentStatus: string;
      if (totalPaid === 0) {
        newPaymentStatus = 'PENDING';
      } else if (totalPaid >= totalAmount) {
        newPaymentStatus = 'PAID';
      } else {
        newPaymentStatus = 'PARTIAL';
      }

      // Update purchase order payment status if changed
      if (purchaseOrder.paymentStatus !== newPaymentStatus) {
        await prisma.purchaseOrder.update({
          where: { id: purchaseOrderId },
          data: {
            paymentStatus: newPaymentStatus,
            updatedBy: userId,
          }
        });

        this.logger.info({
          purchaseOrderId,
          oldPaymentStatus: purchaseOrder.paymentStatus,
          newPaymentStatus,
          totalPaid,
          totalAmount,
          userId
        }, 'Purchase order payment status updated');
      } else {
        this.logger.debug({
          purchaseOrderId,
          paymentStatus: newPaymentStatus,
          totalPaid,
          totalAmount,
          userId
        }, 'Purchase order payment status unchanged');
      }
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId,
        userId
      }, 'Failed to update purchase order payment status');
      // Don't throw error to avoid breaking the main payment creation
    }
  }

  // Payment Terms related methods
  async getPaymentTermsSummary(supplierId: string) {
    this.logger.trace({ supplierId }, 'Getting payment terms summary');
    return this.paymentTermsService.getPaymentTermsSummary(supplierId);
  }

  async validatePaymentTerms(supplierId: string, createPaymentDto: CreateSupplierPaymentDto, userId: string) {
    this.logger.trace({ supplierId, userId }, 'Validating payment terms');

    try {
      // Run the same validation as payment creation but don't create the payment
      await this.validatePaymentBusinessRules(createPaymentDto, supplierId);

      // Calculate payment terms
      const paymentTermsCalc = await this.paymentTermsService.calculatePaymentTerms(
        supplierId,
        createPaymentDto.purchaseOrderId,
        createPaymentDto.effectivePaymentTerms,
      );

      this.logger.info({
        supplierId,
        purchaseOrderId: createPaymentDto.purchaseOrderId,
        paymentTermsCalculation: paymentTermsCalc,
        userId
      }, 'Payment terms validation completed successfully');

      return {
        valid: true,
        paymentTermsCalculation: paymentTermsCalc,
        message: 'Validasi termin pembayaran berhasil',
      };
    } catch (error) {
      this.logger.warn({
        err: error,
        supplierId,
        purchaseOrderId: createPaymentDto.purchaseOrderId,
        userId
      }, 'Payment terms validation failed');

      return {
        valid: false,
        error: error.message,
        message: 'Validasi termin pembayaran gagal',
      };
    }
  }

  /**
   * Validate preconditions before payment creation within transaction
   */
  private async validatePaymentCreationPreconditions(
    prisma: any,
    createPaymentDto: CreateSupplierPaymentDto,
    supplierId: string,
    transactionId: string
  ): Promise<void> {
    this.logger.trace({
      transactionId,
      supplierId,
      purchaseOrderId: createPaymentDto.purchaseOrderId
    }, 'Validating payment creation preconditions');

    // Verify supplier still exists and is active
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, status: true, name: true }
    });

    if (!supplier) {
      throw new BadRequestException(`Supplier ${supplierId} tidak ditemukan`);
    }

    if (supplier.status !== 'ACTIVE') {
      throw new BadRequestException(`Supplier ${supplier.name} tidak aktif`);
    }

    // Verify purchase order if provided
    if (createPaymentDto.purchaseOrderId) {
      const purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: createPaymentDto.purchaseOrderId },
        select: {
          id: true,
          supplierId: true,
          status: true,
          paymentStatus: true,
          totalAmount: true
        }
      });

      if (!purchaseOrder) {
        throw new BadRequestException(`Purchase order ${createPaymentDto.purchaseOrderId} tidak ditemukan`);
      }

      if (purchaseOrder.supplierId !== supplierId) {
        throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
      }

      if (purchaseOrder.paymentStatus === PaymentStatus.PAID) {
        throw new BadRequestException('Purchase order sudah lunas');
      }
    }

    // Verify goods receipt if provided
    if (createPaymentDto.goodsReceiptId) {
      const goodsReceipt = await prisma.goodsReceipt.findUnique({
        where: { id: createPaymentDto.goodsReceiptId },
        select: {
          id: true,
          purchaseOrder: {
            select: { supplierId: true }
          }
        }
      });

      if (!goodsReceipt) {
        throw new BadRequestException(`Goods receipt ${createPaymentDto.goodsReceiptId} tidak ditemukan`);
      }

      if (goodsReceipt.purchaseOrder?.supplierId !== supplierId) {
        throw new BadRequestException('Goods receipt tidak sesuai dengan supplier');
      }
    }

    this.logger.debug({
      transactionId,
      supplierId,
      validationsPassed: true
    }, 'Payment creation preconditions validated successfully');
  }

  /**
   * Update purchase order payment status with enhanced error handling
   */
  private async updatePurchaseOrderPaymentStatusWithErrorHandling(
    prisma: any,
    purchaseOrderId: string,
    userId: string,
    transactionId: string
  ): Promise<void> {
    this.logger.trace({
      transactionId,
      purchaseOrderId,
      userId
    }, 'Updating purchase order payment status with error handling');

    try {
      await this.updatePurchaseOrderPaymentStatus(prisma, purchaseOrderId, userId);

      this.logger.debug({
        transactionId,
        purchaseOrderId
      }, 'Purchase order payment status updated successfully');

    } catch (error) {
      this.logger.error({
        err: error,
        transactionId,
        purchaseOrderId,
        userId
      }, 'Failed to update purchase order payment status');

      // Re-throw to fail the transaction
      throw new Error(`Failed to update purchase order payment status: ${error.message}`);
    }
  }

  /**
   * Validate payment creation results within transaction
   */
  private async validatePaymentCreationResults(
    prisma: any,
    payment: any,
    transactionId: string
  ): Promise<void> {
    this.logger.trace({
      transactionId,
      paymentId: payment.id
    }, 'Validating payment creation results');

    // Verify payment was created with correct data
    const createdPayment = await prisma.supplierPayment.findUnique({
      where: { id: payment.id },
      select: {
        id: true,
        reference: true,
        amount: true,
        status: true,
        supplierId: true,
        purchaseOrderId: true
      }
    });

    if (!createdPayment) {
      throw new Error(`Payment ${payment.id} was not found after creation`);
    }

    if (createdPayment.status !== PaymentStatus.PENDING && createdPayment.status !== PaymentStatus.PAID) {
      throw new Error(`Payment ${payment.id} has invalid status: ${createdPayment.status}`);
    }

    // Verify reference uniqueness
    const duplicateReference = await prisma.supplierPayment.findFirst({
      where: {
        reference: createdPayment.reference,
        id: { not: payment.id }
      },
      select: { id: true }
    });

    if (duplicateReference) {
      throw new Error(`Payment reference ${createdPayment.reference} is not unique`);
    }

    this.logger.debug({
      transactionId,
      paymentId: payment.id,
      reference: createdPayment.reference,
      validationsPassed: true
    }, 'Payment creation results validated successfully');
  }
}
