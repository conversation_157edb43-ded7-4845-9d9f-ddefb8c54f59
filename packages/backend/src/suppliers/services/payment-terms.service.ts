import { Injectable, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { PaymentStatus } from '@prisma/client';

export interface PaymentTermsCalculation {
  effectivePaymentTerms: number;
  calculatedDueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL';
}

export interface PaymentSchedule {
  purchaseOrderId: string;
  orderNumber: string;
  orderDate: Date;
  totalAmount: number;
  paymentTerms: number;
  dueDate: Date;
  isOverdue: boolean;
  daysOverdue: number;
  totalPaid: number;
  remainingAmount: number;
  paymentStatus: PaymentStatus;
}

export interface PaymentTermsSummary {
  supplierId: string;
  supplierName: string;
  defaultPaymentTerms: number | null;
  totalOutstanding: number;
  totalOverdue: number;
  overdueCount: number;
  averagePaymentDays: number;
  complianceRate: number; // Percentage of payments made within terms
  paymentSchedules: PaymentSchedule[];
}

@Injectable()
export class PaymentTermsService {
  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(PaymentTermsService.name)
    private readonly logger: PinoLogger,
  ) {}

  // Monitoring service will be injected later to avoid circular dependency
  private monitoringService: any;

  /**
   * Calculate payment terms for a given purchase order or supplier
   * Priority: Purchase Order terms > Supplier default terms > 0 (immediate)
   * Uses read transaction for data consistency when multiple entities are involved
   */
  async calculatePaymentTerms(
    supplierId: string,
    purchaseOrderId?: string,
    manualTerms?: number,
  ): Promise<PaymentTermsCalculation> {
    this.logger.trace({ supplierId, purchaseOrderId, manualTerms }, 'Calculating payment terms with transaction isolation');

    try {
      // Priority 1: Manual terms (if provided) - no database access needed
      if (manualTerms !== undefined && manualTerms !== null) {
        const result: PaymentTermsCalculation = {
          effectivePaymentTerms: manualTerms,
          calculatedDueDate: new Date(Date.now() + (manualTerms * 24 * 60 * 60 * 1000)),
          isOverdue: false, // Manual terms are for new payments, so not overdue
          daysOverdue: 0,
          source: 'MANUAL',
        };

        this.logger.debug({ supplierId, manualTerms, result }, 'Using manual payment terms');
        return result;
      }

      // For database operations, use read transaction for consistency
      const result = await this.prisma.$transaction(async (prisma) => {
        let effectivePaymentTerms = 0;
        let source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL' = 'SUPPLIER_DEFAULT';
        let orderDate = new Date();

        // Priority 2: Purchase order terms
        if (purchaseOrderId) {
          const purchaseOrder = await prisma.purchaseOrder.findUnique({
            where: { id: purchaseOrderId },
            select: { paymentTerms: true, orderDate: true, supplierId: true },
          });

          if (!purchaseOrder) {
            throw new BadRequestException('Purchase order tidak ditemukan');
          }

          if (purchaseOrder.supplierId !== supplierId) {
            throw new BadRequestException('Purchase order tidak sesuai dengan supplier');
          }

          orderDate = purchaseOrder.orderDate;

          if (purchaseOrder.paymentTerms !== null && purchaseOrder.paymentTerms > 0) {
            effectivePaymentTerms = purchaseOrder.paymentTerms;
            source = 'PURCHASE_ORDER';
            this.logger.debug({
              supplierId,
              purchaseOrderId,
              paymentTerms: purchaseOrder.paymentTerms
            }, 'Using purchase order payment terms');
          } else {
            // Fall back to supplier default - read within same transaction
            const supplier = await prisma.supplier.findUnique({
              where: { id: supplierId },
              select: { paymentTerms: true },
            });

            if (supplier?.paymentTerms && supplier.paymentTerms > 0) {
              effectivePaymentTerms = supplier.paymentTerms;
              source = 'SUPPLIER_DEFAULT';
              this.logger.debug({
                supplierId,
                paymentTerms: supplier.paymentTerms
              }, 'Using supplier default payment terms (fallback from PO)');
            }
          }
        }
        // Priority 3: Supplier default terms only
        else {
          const supplier = await prisma.supplier.findUnique({
            where: { id: supplierId },
            select: { paymentTerms: true },
          });

          if (!supplier) {
            throw new BadRequestException('Supplier tidak ditemukan');
          }

          if (supplier.paymentTerms && supplier.paymentTerms > 0) {
            effectivePaymentTerms = supplier.paymentTerms;
            source = 'SUPPLIER_DEFAULT';
            this.logger.debug({
              supplierId,
              paymentTerms: supplier.paymentTerms
            }, 'Using supplier default payment terms');
          }
        }

        // Calculate due date
        const calculatedDueDate = new Date(orderDate);
        calculatedDueDate.setDate(calculatedDueDate.getDate() + effectivePaymentTerms);

        // Check if overdue
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Start of today
        const isOverdue = calculatedDueDate < today;
        const daysOverdue = isOverdue
          ? Math.floor((today.getTime() - calculatedDueDate.getTime()) / (1000 * 60 * 60 * 24))
          : 0;

        return {
          effectivePaymentTerms,
          calculatedDueDate,
          isOverdue,
          daysOverdue,
          source,
        };
      }, {
        isolationLevel: 'ReadCommitted' // Ensure consistent reads
      });

      this.logger.debug({
        supplierId,
        purchaseOrderId,
        result
      }, 'Payment terms calculated with transaction isolation');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId,
        purchaseOrderId,
        manualTerms
      }, 'Failed to calculate payment terms');
      throw error;
    }
  }

  /**
   * Get payment schedule for a specific purchase order
   */
  async getPaymentSchedule(purchaseOrderId: string): Promise<PaymentSchedule> {
    this.logger.trace({ purchaseOrderId }, 'Getting payment schedule');

    try {
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
      });

      if (!purchaseOrder) {
        throw new BadRequestException('Purchase order tidak ditemukan');
      }

      // Calculate payment terms
      const paymentTermsCalc = await this.calculatePaymentTerms(
        purchaseOrder.supplierId,
        purchaseOrderId,
      );

      // Calculate payment amounts
      const totalPaid = purchaseOrder.payments.reduce(
        (sum, payment) => sum + Number(payment.amount),
        0,
      );
      const totalAmount = Number(purchaseOrder.totalAmount);
      const remainingAmount = totalAmount - totalPaid;

      const schedule: PaymentSchedule = {
        purchaseOrderId,
        orderNumber: purchaseOrder.orderNumber,
        orderDate: purchaseOrder.orderDate,
        totalAmount,
        paymentTerms: paymentTermsCalc.effectivePaymentTerms,
        dueDate: paymentTermsCalc.calculatedDueDate,
        isOverdue: paymentTermsCalc.isOverdue,
        daysOverdue: paymentTermsCalc.daysOverdue,
        totalPaid,
        remainingAmount,
        paymentStatus: purchaseOrder.paymentStatus,
      };

      this.logger.debug({ purchaseOrderId, schedule }, 'Payment schedule calculated');

      return schedule;
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId
      }, 'Failed to get payment schedule');
      throw error;
    }
  }

  /**
   * Get comprehensive payment terms summary for a supplier
   * Uses transaction for data consistency across multiple related queries
   */
  async getPaymentTermsSummary(supplierId: string): Promise<PaymentTermsSummary> {
    this.logger.trace({ supplierId }, 'Getting payment terms summary with transaction consistency');

    try {
      // Use transaction to ensure consistent data across all related queries
      const result = await this.prisma.$transaction(async (prisma) => {
        // Get supplier info
        const supplier = await prisma.supplier.findUnique({
          where: { id: supplierId },
          select: { name: true, paymentTerms: true },
        });

        if (!supplier) {
          throw new BadRequestException('Supplier tidak ditemukan');
        }

        // Get all outstanding purchase orders within the same transaction
        const purchaseOrders = await prisma.purchaseOrder.findMany({
          where: {
            supplierId,
            paymentStatus: {
              in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
            },
          },
          include: {
            payments: {
              where: {
                status: {
                  in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
                },
              },
              select: { amount: true },
            },
          },
          orderBy: { orderDate: 'desc' },
        });

      // Calculate payment schedules for each order
      const paymentSchedules: PaymentSchedule[] = [];
      let totalOutstanding = 0;
      let totalOverdue = 0;
      let overdueCount = 0;

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);
        paymentSchedules.push(schedule);

        totalOutstanding += schedule.remainingAmount;
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          totalOverdue += schedule.remainingAmount;
          overdueCount++;
        }
      }

        // Calculate compliance metrics within the same transaction
        const allPayments = await prisma.supplierPayment.findMany({
          where: {
            supplierId,
            status: PaymentStatus.PAID,
            purchaseOrderId: { not: null },
          },
          include: {
            purchaseOrder: {
              select: { orderDate: true },
            },
          },
        });

      let totalPaymentDays = 0;
      let onTimePayments = 0;

      for (const payment of allPayments) {
        if (payment.purchaseOrder && payment.effectivePaymentTerms !== null) {
          const orderDate = payment.purchaseOrder.orderDate;
          const paymentDate = payment.paymentDate;
          const actualDays = Math.floor(
            (paymentDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24),
          );
          
          totalPaymentDays += actualDays;
          
          if (actualDays <= payment.effectivePaymentTerms) {
            onTimePayments++;
          }
        }
      }

      const averagePaymentDays = allPayments.length > 0 
        ? Math.round(totalPaymentDays / allPayments.length)
        : 0;
      
      const complianceRate = allPayments.length > 0 
        ? Math.round((onTimePayments / allPayments.length) * 100)
        : 100;

        return {
          supplierId,
          supplierName: supplier.name,
          defaultPaymentTerms: supplier.paymentTerms,
          totalOutstanding,
          totalOverdue,
          overdueCount,
          averagePaymentDays,
          complianceRate,
          paymentSchedules,
        };
      }, {
        isolationLevel: 'ReadCommitted' // Ensure consistent reads across all queries
      });

      this.logger.debug({ supplierId, summary: result }, 'Payment terms summary calculated with transaction consistency');

      return result;
    } catch (error) {
      this.logger.error({
        err: error,
        supplierId
      }, 'Failed to get payment terms summary');
      throw error;
    }
  }

  /**
   * Get all overdue payments across all suppliers
   */
  async getOverduePayments(): Promise<PaymentSchedule[]> {
    this.logger.trace({}, 'Getting all overdue payments');

    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get all purchase orders with outstanding amounts
      const purchaseOrders = await this.prisma.purchaseOrder.findMany({
        where: {
          paymentStatus: {
            in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
          },
        },
        include: {
          supplier: {
            select: { name: true, paymentTerms: true },
          },
          payments: {
            where: {
              status: {
                in: [PaymentStatus.PAID, PaymentStatus.PARTIAL],
              },
            },
            select: { amount: true },
          },
        },
        orderBy: { orderDate: 'asc' },
      });

      const overdueSchedules: PaymentSchedule[] = [];

      for (const order of purchaseOrders) {
        const schedule = await this.getPaymentSchedule(order.id);

        // Only include if overdue and has remaining amount
        if (schedule.isOverdue && schedule.remainingAmount > 0) {
          overdueSchedules.push(schedule);
        }
      }

      this.logger.info({
        overdueCount: overdueSchedules.length,
        totalOverdueAmount: overdueSchedules.reduce((sum, s) => sum + s.remainingAmount, 0)
      }, 'Overdue payments retrieved');

      return overdueSchedules;
    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to get overdue payments');
      throw error;
    }
  }

  /**
   * Update payment status based on payment terms and current date
   * This method can be called periodically to update overdue statuses
   * Uses batch transactions to ensure atomicity and data consistency
   */
  async updateOverduePaymentStatuses(): Promise<{ updated: number; errors: number }> {
    this.logger.trace({}, 'Updating overdue payment statuses with transaction safety');

    try {
      const overdueSchedules = await this.getOverduePayments();

      if (overdueSchedules.length === 0) {
        this.logger.info({}, 'No overdue payments found to update');
        return { updated: 0, errors: 0 };
      }

      // Filter schedules that actually need updating
      const schedulesToUpdate = overdueSchedules.filter(
        schedule => schedule.paymentStatus !== PaymentStatus.OVERDUE
      );

      if (schedulesToUpdate.length === 0) {
        this.logger.info({
          totalOverdue: overdueSchedules.length,
          alreadyUpdated: overdueSchedules.length
        }, 'All overdue payments already have correct status');
        return { updated: 0, errors: 0 };
      }

      this.logger.debug({
        totalOverdue: overdueSchedules.length,
        needingUpdate: schedulesToUpdate.length
      }, 'Starting batch overdue status update');

      // Process updates in batches to avoid transaction timeouts
      const BATCH_SIZE = 50;
      let totalUpdated = 0;
      let totalErrors = 0;

      for (let i = 0; i < schedulesToUpdate.length; i += BATCH_SIZE) {
        const batch = schedulesToUpdate.slice(i, i + BATCH_SIZE);

        try {
          const batchResult = await this.updateOverduePaymentsBatch(batch);
          totalUpdated += batchResult.updated;
          totalErrors += batchResult.errors;

          this.logger.debug({
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length,
            batchUpdated: batchResult.updated,
            batchErrors: batchResult.errors
          }, 'Completed overdue payment batch update');

        } catch (error) {
          totalErrors += batch.length;
          this.logger.error({
            err: error,
            batchNumber: Math.floor(i / BATCH_SIZE) + 1,
            batchSize: batch.length
          }, 'Failed to process overdue payment batch');
        }
      }

      this.logger.info({
        totalProcessed: schedulesToUpdate.length,
        updated: totalUpdated,
        errors: totalErrors,
        successRate: totalUpdated / (totalUpdated + totalErrors) * 100
      }, 'Overdue payment status update completed');

      return { updated: totalUpdated, errors: totalErrors };

    } catch (error) {
      this.logger.error({
        err: error
      }, 'Failed to update overdue payment statuses');
      throw error;
    }
  }

  /**
   * Update a batch of overdue payments within a single transaction
   * Ensures atomicity - either all updates succeed or all fail
   * Implements comprehensive error handling and rollback mechanisms
   */
  private async updateOverduePaymentsBatch(
    schedules: any[]
  ): Promise<{ updated: number; errors: number }> {
    this.logger.trace({
      batchSize: schedules.length
    }, 'Processing overdue payment batch in transaction with comprehensive error handling');

    const transactionStartTime = Date.now();
    let transactionId: string | undefined;

    try {
      // Generate unique transaction ID for tracking
      transactionId = `overdue_batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      this.logger.info({
        transactionId,
        batchSize: schedules.length,
        scheduleIds: schedules.map(s => s.purchaseOrderId)
      }, 'Starting overdue payment batch transaction');

      const result = await this.prisma.$transaction(async (prisma) => {
        // Pre-transaction validation
        await this.validateBatchUpdatePreconditions(prisma, schedules, transactionId!);

        const updateResults: Array<{ success: boolean; purchaseOrderId: string; error?: any }> = [];

        // Process each update with individual error tracking
        for (const schedule of schedules) {
          try {
            // Verify the purchase order still exists and is in correct state
            const currentOrder = await prisma.purchaseOrder.findUnique({
              where: { id: schedule.purchaseOrderId },
              select: {
                id: true,
                orderNumber: true,
                paymentStatus: true,
                totalAmount: true,
                supplierId: true
              }
            });

            if (!currentOrder) {
              throw new Error(`Purchase order ${schedule.purchaseOrderId} no longer exists`);
            }

            // Skip if already overdue (idempotent operation)
            if (currentOrder.paymentStatus === PaymentStatus.OVERDUE) {
              this.logger.debug({
                transactionId,
                purchaseOrderId: schedule.purchaseOrderId,
                orderNumber: currentOrder.orderNumber
              }, 'Purchase order already marked as OVERDUE - skipping');

              updateResults.push({
                success: true,
                purchaseOrderId: schedule.purchaseOrderId
              });
              continue;
            }

            // Perform the update with optimistic locking
            const updatedOrder = await prisma.purchaseOrder.update({
              where: {
                id: schedule.purchaseOrderId,
                // Optimistic locking - ensure status hasn't changed
                paymentStatus: currentOrder.paymentStatus
              },
              data: {
                paymentStatus: PaymentStatus.OVERDUE,
                updatedAt: new Date()
              },
              select: { id: true, orderNumber: true, paymentStatus: true }
            });

            this.logger.debug({
              transactionId,
              purchaseOrderId: schedule.purchaseOrderId,
              orderNumber: updatedOrder.orderNumber,
              previousStatus: currentOrder.paymentStatus,
              newStatus: updatedOrder.paymentStatus,
              daysOverdue: schedule.daysOverdue
            }, 'Successfully updated purchase order status to OVERDUE');

            updateResults.push({
              success: true,
              purchaseOrderId: schedule.purchaseOrderId
            });

          } catch (updateError) {
            this.logger.error({
              err: updateError,
              transactionId,
              purchaseOrderId: schedule.purchaseOrderId,
              orderNumber: schedule.orderNumber,
              daysOverdue: schedule.daysOverdue
            }, 'Failed to update individual purchase order in batch transaction');

            updateResults.push({
              success: false,
              purchaseOrderId: schedule.purchaseOrderId,
              error: updateError
            });

            // For batch operations, we can choose to continue or fail entire transaction
            // Here we fail the entire transaction for data consistency
            throw new Error(`Batch update failed at purchase order ${schedule.purchaseOrderId}: ${updateError.message}`);
          }
        }

        // Post-transaction validation
        await this.validateBatchUpdateResults(prisma, updateResults, transactionId!);

        const successCount = updateResults.filter(r => r.success).length;
        const errorCount = updateResults.filter(r => !r.success).length;

        this.logger.info({
          transactionId,
          totalProcessed: updateResults.length,
          successful: successCount,
          failed: errorCount,
          duration: Date.now() - transactionStartTime
        }, 'Batch transaction completed successfully');

        return {
          updated: successCount,
          errors: errorCount
        };

      }, {
        timeout: 30000, // 30 second timeout for batch operations
        isolationLevel: 'ReadCommitted' // Prevent dirty reads
      });

      this.logger.info({
        transactionId,
        batchSize: schedules.length,
        updated: result.updated,
        errors: result.errors,
        duration: Date.now() - transactionStartTime
      }, 'Overdue payment batch transaction completed successfully');

      return result;

    } catch (error) {
      const duration = Date.now() - transactionStartTime;

      // Comprehensive error logging
      this.logger.error({
        err: error,
        transactionId,
        batchSize: schedules.length,
        duration,
        errorType: error.constructor.name,
        errorCode: error.code,
        errorMessage: error.message,
        scheduleIds: schedules.map(s => s.purchaseOrderId)
      }, 'Transaction failed for overdue payment batch - all changes automatically rolled back');

      // Handle specific error types
      if (error.code === 'P2034') {
        this.logger.warn({
          transactionId,
          batchSize: schedules.length
        }, 'Transaction failed due to write conflict - data was modified by another process');
      } else if (error.code === 'P2025') {
        this.logger.warn({
          transactionId,
          batchSize: schedules.length
        }, 'Transaction failed due to record not found - data may have been deleted');
      } else if (error.message?.includes('timeout')) {
        this.logger.warn({
          transactionId,
          batchSize: schedules.length,
          duration
        }, 'Transaction failed due to timeout - consider reducing batch size');
      }

      // Return all as errors since transaction failed and rolled back
      return {
        updated: 0,
        errors: schedules.length
      };
    }
  }

  /**
   * Validate preconditions before starting batch update transaction
   */
  private async validateBatchUpdatePreconditions(
    prisma: any,
    schedules: any[],
    transactionId: string
  ): Promise<void> {
    this.logger.trace({
      transactionId,
      batchSize: schedules.length
    }, 'Validating batch update preconditions');

    // Check if all purchase orders exist
    const purchaseOrderIds = schedules.map(s => s.purchaseOrderId);
    const existingOrders = await prisma.purchaseOrder.findMany({
      where: { id: { in: purchaseOrderIds } },
      select: { id: true, paymentStatus: true }
    });

    if (existingOrders.length !== purchaseOrderIds.length) {
      const missingIds = purchaseOrderIds.filter(
        id => !existingOrders.find(order => order.id === id)
      );
      throw new Error(`Purchase orders not found: ${missingIds.join(', ')}`);
    }

    // Check for any orders that are already in terminal states
    const terminalStatuses = [PaymentStatus.PAID];
    const terminalOrders = existingOrders.filter(
      order => terminalStatuses.includes(order.paymentStatus)
    );

    if (terminalOrders.length > 0) {
      this.logger.warn({
        transactionId,
        terminalOrderIds: terminalOrders.map(o => o.id)
      }, 'Some orders are in terminal payment status - skipping validation error');
    }

    this.logger.debug({
      transactionId,
      validatedOrders: existingOrders.length,
      terminalOrders: terminalOrders.length
    }, 'Batch update preconditions validated successfully');
  }

  /**
   * Validate results after batch update transaction
   */
  private async validateBatchUpdateResults(
    prisma: any,
    updateResults: Array<{ success: boolean; purchaseOrderId: string; error?: any }>,
    transactionId: string
  ): Promise<void> {
    this.logger.trace({
      transactionId,
      resultCount: updateResults.length
    }, 'Validating batch update results');

    const successfulIds = updateResults
      .filter(r => r.success)
      .map(r => r.purchaseOrderId);

    if (successfulIds.length === 0) {
      this.logger.debug({
        transactionId
      }, 'No successful updates to validate');
      return;
    }

    // Verify that all successful updates actually have OVERDUE status
    const updatedOrders = await prisma.purchaseOrder.findMany({
      where: {
        id: { in: successfulIds },
        paymentStatus: PaymentStatus.OVERDUE
      },
      select: { id: true, paymentStatus: true }
    });

    if (updatedOrders.length !== successfulIds.length) {
      const inconsistentIds = successfulIds.filter(
        id => !updatedOrders.find(order => order.id === id)
      );
      throw new Error(`Post-update validation failed - orders not in OVERDUE status: ${inconsistentIds.join(', ')}`);
    }

    this.logger.debug({
      transactionId,
      validatedUpdates: updatedOrders.length
    }, 'Batch update results validated successfully');
  }
}
