import { Module } from '@nestjs/common';
import { SuppliersService } from './suppliers.service';
import { SuppliersController } from './suppliers.controller';
import { PaymentTermsService } from './services/payment-terms.service';
import { PaymentTermsSchedulerService } from './services/payment-terms-scheduler.service';
import { PaymentTermsMonitoringService } from './services/payment-terms-monitoring.service';
import { PrismaModule } from '../prisma/prisma.module';
import { SettingsModule } from '../settings/settings.module';
import { FileUploadService } from '../common/services/file-upload.service';
import { ReferenceGeneratorService } from '../common/services/reference-generator.service';
import { ImportExportService } from './services/import-export.service';
import { SupplierCodeGeneratorService } from './supplier-code-generator.service';

@Module({
  imports: [PrismaModule, SettingsModule],
  controllers: [SuppliersController],
  providers: [SuppliersService, PaymentTermsService, PaymentTermsSchedulerService, PaymentTermsMonitoringService, FileUploadService, ReferenceGeneratorService, ImportExportService, SupplierCodeGeneratorService],
  exports: [SuppliersService, PaymentTermsService, PaymentTermsSchedulerService, PaymentTermsMonitoringService],
})
export class SuppliersModule {}
