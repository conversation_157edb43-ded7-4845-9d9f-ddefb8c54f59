# Payment Terms Transaction Boundaries

## Overview
This document defines clear transaction boundaries for all payment-related operations to ensure ACID compliance and data consistency.

## Transaction Principles

### ✅ **INSIDE TRANSACTION SCOPE**
- Database read/write operations that must be atomic
- Multi-entity updates that require consistency
- Business logic that affects multiple tables
- Data validation that depends on current database state

### ❌ **OUTSIDE TRANSACTION SCOPE**
- External API calls (logging services, notifications)
- File operations (unless critical to data integrity)
- Email/SMS notifications
- Third-party service integrations
- Non-critical logging operations

## Service-by-Service Transaction Boundaries

### 1. **SuppliersService**

#### ✅ Payment Creation (`createPayment`)
```typescript
// TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Create payment record
  const payment = await prisma.supplierPayment.create({...});
  
  // ✅ Update purchase order payment status
  await this.updatePurchaseOrderPaymentStatus(prisma, purchaseOrderId, userId);
  
  return payment;
});

// EXTERNAL OPERATIONS (after transaction):
// ❌ Compliance event logging
await this.paymentTermsMonitoringService.logComplianceEvent(...);
```

#### ✅ Payment Update (`updatePayment`)
```typescript
// TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Update payment record
  const payment = await prisma.supplierPayment.update({...});
  
  // ✅ Update related purchase order status
  await this.updatePurchaseOrderPaymentStatus(prisma, purchaseOrderId, userId);
  
  return payment;
});
```

#### ✅ Payment Deletion (`deletePayment`)
```typescript
// TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Delete payment record
  await prisma.supplierPayment.delete({...});
  
  // ✅ Update purchase order payment status
  await this.updatePurchaseOrderPaymentStatus(prisma, purchaseOrderId, userId);
});
```

### 2. **PaymentTermsService**

#### ✅ Payment Terms Calculation (`calculatePaymentTerms`)
```typescript
// TRANSACTION SCOPE (for database operations):
await this.prisma.$transaction(async (prisma) => {
  // ✅ Read purchase order terms
  const purchaseOrder = await prisma.purchaseOrder.findUnique({...});
  
  // ✅ Read supplier default terms (if needed)
  const supplier = await prisma.supplier.findUnique({...});
  
  return calculatedTerms;
}, {
  isolationLevel: 'ReadCommitted' // Ensure consistent reads
});
```

#### ✅ Payment Terms Summary (`getPaymentTermsSummary`)
```typescript
// TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Read supplier info
  const supplier = await prisma.supplier.findUnique({...});
  
  // ✅ Read outstanding purchase orders
  const purchaseOrders = await prisma.purchaseOrder.findMany({...});
  
  // ✅ Read payment history
  const allPayments = await prisma.supplierPayment.findMany({...});
  
  return summary;
}, {
  isolationLevel: 'ReadCommitted'
});
```

#### ✅ Overdue Payment Updates (`updateOverduePaymentStatuses`)
```typescript
// BATCH TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Update multiple purchase orders atomically
  const updatePromises = schedules.map(schedule => 
    prisma.purchaseOrder.update({
      where: { id: schedule.purchaseOrderId },
      data: { paymentStatus: PaymentStatus.OVERDUE }
    })
  );
  
  await Promise.all(updatePromises);
}, {
  timeout: 30000 // Extended timeout for batch operations
});
```

### 3. **PaymentTermsSchedulerService**

#### ✅ Scheduled Overdue Updates (`handleUpdateOverduePayments`)
```typescript
// TRANSACTION SCOPE:
const result = await this.paymentTermsService.updateOverduePaymentStatuses();

// EXTERNAL OPERATIONS (after transaction):
// ❌ Success/failure logging
this.logger.info({ updated: result.updated }, 'Scheduled update completed');
```

#### ✅ Report Generation (`handleGenerateOverdueReport`)
```typescript
// TRANSACTION SCOPE:
const overduePayments = await this.paymentTermsService.getOverduePayments();

// EXTERNAL OPERATIONS (after data collection):
// ❌ Email notifications
// ❌ Report file generation
// ❌ Third-party integrations
```

### 4. **PaymentTermsMonitoringService**

#### ✅ Compliance Report (`generateComplianceReport`)
```typescript
// TRANSACTION SCOPE:
await this.prisma.$transaction(async (prisma) => {
  // ✅ Read all suppliers with payments
  const suppliers = await prisma.supplier.findMany({...});
  
  // ✅ Calculate metrics for each supplier
  for (const supplier of suppliers) {
    const metrics = await this.calculateSupplierMetricsInTransaction(prisma, supplier.id);
    // Process metrics...
  }
  
  return report;
}, {
  isolationLevel: 'ReadCommitted'
});
```

#### ✅ Supplier Metrics (`calculateSupplierMetricsInTransaction`)
```typescript
// TRANSACTION SCOPE (within parent transaction):
// ✅ Read supplier payments
const payments = await prisma.supplierPayment.findMany({...});

// ✅ Read outstanding orders
const outstandingOrders = await prisma.purchaseOrder.findMany({...});

// ✅ Calculate compliance metrics
return metrics;
```

## Error Handling Patterns

### 1. **Transaction Rollback**
```typescript
try {
  const result = await this.prisma.$transaction(async (prisma) => {
    // All database operations here
    // If any operation fails, entire transaction rolls back
    return result;
  });
  
  // External operations after successful transaction
  await this.performExternalOperations(result);
  
} catch (error) {
  // Transaction automatically rolled back
  this.logger.error({ err: error }, 'Transaction failed - all changes rolled back');
  throw error;
}
```

### 2. **Batch Processing with Error Isolation**
```typescript
// Process in batches to avoid transaction timeouts
const BATCH_SIZE = 50;
for (let i = 0; i < items.length; i += BATCH_SIZE) {
  const batch = items.slice(i, i + BATCH_SIZE);
  
  try {
    await this.processBatchInTransaction(batch);
  } catch (error) {
    // Log batch failure but continue with next batch
    this.logger.error({ batchNumber: i / BATCH_SIZE, error }, 'Batch failed');
  }
}
```

### 3. **External Operation Failure Handling**
```typescript
// Database transaction succeeds
const result = await this.prisma.$transaction(async (prisma) => {
  return await this.performDatabaseOperations(prisma);
});

// External operations (non-critical)
try {
  await this.sendNotifications(result);
} catch (externalError) {
  // Don't fail the entire operation for external failures
  this.logger.warn({ err: externalError }, 'External operation failed but transaction succeeded');
}
```

## Transaction Configuration

### 1. **Isolation Levels**
```typescript
// For read-heavy operations requiring consistency
{
  isolationLevel: 'ReadCommitted'
}

// For write-heavy operations requiring serialization
{
  isolationLevel: 'Serializable'
}
```

### 2. **Timeouts**
```typescript
// Standard operations
{
  timeout: 10000 // 10 seconds
}

// Batch operations
{
  timeout: 30000 // 30 seconds
}

// Large report generation
{
  timeout: 60000 // 60 seconds
}
```

## Best Practices

### ✅ **DO**
- Keep transactions as short as possible
- Use appropriate isolation levels
- Handle external operations outside transactions
- Use batch processing for large datasets
- Log transaction boundaries clearly
- Implement proper error handling and rollback

### ❌ **DON'T**
- Include external API calls in transactions
- Use overly long transaction timeouts
- Ignore transaction failures
- Mix critical and non-critical operations in same transaction
- Use transactions for read-only operations unless consistency is critical
- Create nested transactions unnecessarily

## Monitoring and Debugging

### 1. **Transaction Logging**
```typescript
this.logger.debug({
  transactionId: 'unique-id',
  operation: 'payment-creation',
  entitiesAffected: ['SupplierPayment', 'PurchaseOrder'],
  transactionCompleted: true
}, 'Transaction completed successfully');
```

### 2. **Performance Monitoring**
```typescript
const startTime = Date.now();
await this.prisma.$transaction(async (prisma) => {
  // Operations...
});
const duration = Date.now() - startTime;

this.logger.info({
  operation: 'batch-overdue-update',
  duration,
  recordsProcessed: count
}, 'Transaction performance metrics');
```

This transaction boundary design ensures:
- **Atomicity**: All related changes succeed or fail together
- **Consistency**: Data remains in valid state across all operations
- **Isolation**: Concurrent operations don't interfere with each other
- **Durability**: Committed changes are permanently stored
- **Performance**: External operations don't block database transactions
- **Reliability**: Proper error handling and rollback mechanisms
