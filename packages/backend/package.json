{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration:help": "turbo run test:integration -h", "test:integration": "ts-node test/integration/run-tests.ts", "test:integration:coverage": "ts-node test/integration/run-tests.ts --coverage", "test:integration:watch": "jest --config test/integration/jest.integration.config.js --watch", "test:all": "pnpm run test && pnpm run test:integration", "test:all:coverage": "pnpm run test:cov && pnpm run test:integration:coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node ./src/scripts/seed.ts", "seed:suppliers": "ts-node ./src/scripts/seed-suppliers.ts", "clean": "rm -rf dist", "logs:watch": "tail -f $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log) | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60", "logs:watch:all": "tail -f logs/*.log* | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60", "logs:pretty": "bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60 < $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log)", "logs:pretty:latest": "bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60 < $(ls -t logs/*.log* | head -1)", "logs:filter:error": "tail -f $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log) | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60 -L warn", "logs:filter:warn": "tail -f $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log) | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60 -L info", "logs:filter:context": "tail -f $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log) | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60", "logs:dev": "tail -f $(ls -t logs/pharmacy-backend-$(date +%Y-%m-%d).log* 2>/dev/null | head -1 || echo logs/pharmacy-backend-$(date +%Y-%m-%d).log) | bun x pino-pretty --levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60", "logs:clean": "find logs -name '*.log*' -mtime +7 -delete && echo 'Cleaned logs older than 7 days'"}, "dependencies": {"@logtail/node": "^0.5.5", "@logtail/pino": "^0.5.5", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.9.0", "@tanstack/react-table": "^8.21.3", "@types/multer": "^1.4.13", "@types/pdfkit": "^0.14.0", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "multer": "^2.0.1", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.1", "pino": "^9.7.0", "pino-abstract-transport": "^2.0.0", "pino-multi-stream": "^6.0.0", "pino-pretty": "^13.0.0", "pino-roll": "^3.1.0", "prisma": "^6.9.0", "puppeteer": "^24.10.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pino": "^7.0.5", "@types/supertest": "^6.0.2", "dotenv": "^16.5.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "pino-tee": "^0.3.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "prisma": {"seed": "ts-node ./src/scripts/seed.ts"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}